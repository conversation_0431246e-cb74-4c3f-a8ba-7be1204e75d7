"""Operations API router for retry and other operations."""

from fastapi import APIRouter, Depends, HTTPException, BackgroundTasks
from typing import Dict, Any, Optional
import logging
import uuid
import os
from datetime import datetime
from pathlib import Path

from ..dependencies import get_indexer, get_job_repository, get_book_repository, get_app_config
from ..models.requests import RetryOperationRequest, JobCreateRequest, ReindexFileRequest
from ..models.responses import (
    OperationStartResponse,
    OperationStatus,
    OperationProgress,
    OperationResults,
    BaseResponse
)
from ebook_indexer.core.indexer import EbookIndexer
from ebook_indexer.database.repository import JobRepository, BookRepository
from ebook_indexer.config.settings import AppConfig


router = APIRouter()
logger = logging.getLogger(__name__)

# In-memory operation tracking (in production, use Redis or database)
_operations: Dict[str, Dict[str, Any]] = {}


@router.post("/retry", response_model=OperationStartResponse)
async def trigger_retry_operation(
    request: RetryOperationRequest,
    background_tasks: BackgroundTasks,
    indexer: EbookIndexer = Depends(get_indexer),
    book_repo: BookRepository = Depends(get_book_repository)
) -> OperationStartResponse:
    """Trigger a retry operation for failed files."""
    try:
        # Generate operation ID
        operation_id = str(uuid.uuid4())

        # Get retry candidates to estimate work
        retry_candidates = book_repo.get_retry_candidates(request.job_id)
        estimated_files = len(retry_candidates)

        if estimated_files == 0:
            raise HTTPException(
                status_code=404,
                detail="No files found that need retry"
            )

        # Initialize operation tracking
        _operations[operation_id] = {
            "id": operation_id,
            "type": "retry",
            "status": "pending",
            "progress": {
                "processed": 0,
                "total": estimated_files,
                "percentage": 0.0,
                "eta_seconds": None,
                "current_item": None
            },
            "results": {
                "successful": 0,
                "failed": 0,
                "still_anomalous": 0
            },
            "started_at": datetime.utcnow(),
            "estimated_completion": None,
            "job_id": request.job_id,
            "filter": request.filter
        }

        # Start retry operation in background
        background_tasks.add_task(
            _execute_retry_operation,
            operation_id,
            request.job_id,
            indexer
        )

        logger.info(f"Started retry operation {operation_id} for job {request.job_id}")

        return OperationStartResponse(
            operation_id=operation_id,
            status="pending",
            estimated_files=estimated_files,
            message=f"Retry operation started for {estimated_files} files"
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to start retry operation: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to start retry operation: {e}")


@router.get("", response_model=list[OperationStatus])
async def list_operations(limit: int = 20) -> list[OperationStatus]:
    """List recent operations."""
    try:
        # Sort operations by start time (most recent first)
        sorted_ops = sorted(
            _operations.values(),
            key=lambda x: x["started_at"],
            reverse=True
        )

        # Limit results
        limited_ops = sorted_ops[:limit]

        return [
            OperationStatus(
                operation_id=op["id"],
                type=op["type"],
                status=op["status"],
                progress=OperationProgress(**op["progress"]),
                results=OperationResults(**op["results"]),
                started_at=op["started_at"],
                estimated_completion=op["estimated_completion"]
            )
            for op in limited_ops
        ]

    except Exception as e:
        logger.error(f"Failed to list operations: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to list operations: {e}")


@router.post("/index", response_model=OperationStartResponse)
async def trigger_index_operation(
    request: JobCreateRequest,
    background_tasks: BackgroundTasks,
    indexer: EbookIndexer = Depends(get_indexer)
) -> OperationStartResponse:
    """Trigger a new indexing operation."""
    try:
        # Generate operation ID
        operation_id = str(uuid.uuid4())

        # Initialize operation tracking
        _operations[operation_id] = {
            "id": operation_id,
            "type": "index",
            "status": "pending",
            "progress": {
                "processed": 0,
                "total": 0,  # Will be updated when scanning starts
                "percentage": 0.0,
                "eta_seconds": None,
                "current_item": None
            },
            "results": {
                "successful": 0,
                "failed": 0,
                "still_anomalous": 0
            },
            "started_at": datetime.utcnow(),
            "estimated_completion": None,
            "root_directories": request.root_directories,
            "resume": request.resume,
            "job_id": request.job_id
        }

        # Start indexing operation in background
        background_tasks.add_task(
            _execute_index_operation,
            operation_id,
            request,
            indexer
        )

        logger.info(f"Started index operation {operation_id} for directories {request.root_directories}")

        return OperationStartResponse(
            operation_id=operation_id,
            status="pending",
            estimated_files=0,  # Will be updated during scanning
            message=f"Indexing operation started for {len(request.root_directories)} directories"
        )

    except Exception as e:
        logger.error(f"Failed to start index operation: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to start index operation: {e}")


@router.post("/reindex-file", response_model=OperationStartResponse)
async def trigger_reindex_file_operation(
    request: ReindexFileRequest,
    background_tasks: BackgroundTasks,
    indexer: EbookIndexer = Depends(get_indexer),
    book_repo: BookRepository = Depends(get_book_repository)
) -> OperationStartResponse:
    """Trigger re-indexing of a single file."""
    try:
        # Validate file exists
        if not os.path.exists(request.file_path):
            raise HTTPException(status_code=404, detail=f"File not found: {request.file_path}")

        # Check if it's a file (not directory)
        if not os.path.isfile(request.file_path):
            raise HTTPException(status_code=400, detail=f"Path is not a file: {request.file_path}")

        # Check if file extension is supported
        file_path = Path(request.file_path)
        file_ext = file_path.suffix.lower()
        supported_extensions = indexer.config.supported_extensions

        if file_ext not in supported_extensions:
            raise HTTPException(
                status_code=400,
                detail=f"File extension '{file_ext}' not supported. Supported: {supported_extensions}"
            )

        # Generate operation ID
        operation_id = str(uuid.uuid4())

        # Initialize operation tracking
        _operations[operation_id] = {
            "id": operation_id,
            "type": "reindex_file",
            "status": "pending",
            "progress": {
                "processed": 0,
                "total": 1,
                "percentage": 0.0,
                "eta_seconds": None,
                "current_item": request.file_path
            },
            "results": {
                "successful": 0,
                "failed": 0,
                "still_anomalous": 0
            },
            "started_at": datetime.utcnow(),
            "estimated_completion": None,
            "file_path": request.file_path,
            "force": request.force
        }

        # Start reindex operation in background
        background_tasks.add_task(
            _execute_reindex_file_operation,
            operation_id,
            request,
            indexer,
            book_repo
        )

        logger.info(f"Started reindex file operation {operation_id} for file {request.file_path}")

        return OperationStartResponse(
            operation_id=operation_id,
            status="pending",
            estimated_files=1,
            message=f"Re-indexing file: {os.path.basename(request.file_path)}"
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to start reindex file operation: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to start reindex file operation: {e}")


@router.delete("/{operation_id}")
async def cancel_operation(operation_id: str) -> BaseResponse:
    """Cancel a running operation."""
    try:
        if operation_id not in _operations:
            raise HTTPException(status_code=404, detail="Operation not found")

        op = _operations[operation_id]

        if op["status"] in ["completed", "failed", "cancelled"]:
            raise HTTPException(
                status_code=400,
                detail=f"Cannot cancel operation with status: {op['status']}"
            )

        # Mark as cancelled
        _operations[operation_id]["status"] = "cancelled"

        logger.info(f"Cancelled operation {operation_id}")

        return BaseResponse(
            success=True,
            message=f"Operation {operation_id} cancelled"
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to cancel operation: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to cancel operation: {e}")


@router.get("/{operation_id}", response_model=OperationStatus)
async def get_operation_status(operation_id: str) -> OperationStatus:
    """Get the status of a specific operation."""
    try:
        if operation_id not in _operations:
            raise HTTPException(status_code=404, detail="Operation not found")

        op = _operations[operation_id]

        return OperationStatus(
            operation_id=op["id"],
            type=op["type"],
            status=op["status"],
            progress=OperationProgress(**op["progress"]),
            results=OperationResults(**op["results"]),
            started_at=op["started_at"],
            estimated_completion=op["estimated_completion"]
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to get operation status: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to get operation status: {e}")


# Background task functions
async def _execute_retry_operation(operation_id: str, job_id: Optional[str], indexer: EbookIndexer):
    """Execute retry operation in background."""
    try:
        # Update status to running
        _operations[operation_id]["status"] = "running"

        # Execute retry
        results = indexer.retry_failed_files(job_id)

        # Update operation with results
        _operations[operation_id].update({
            "status": "completed",
            "progress": {
                "processed": results["retried"],
                "total": results["retried"],
                "percentage": 100.0,
                "eta_seconds": 0,
                "current_item": None
            },
            "results": {
                "successful": results["successful"],
                "failed": results["still_failed"],
                "still_anomalous": 0  # Not tracked in retry
            }
        })

        logger.info(f"Completed retry operation {operation_id}")

    except Exception as e:
        logger.error(f"Retry operation {operation_id} failed: {e}")
        _operations[operation_id]["status"] = "failed"


async def _execute_index_operation(operation_id: str, request: JobCreateRequest, indexer: EbookIndexer):
    """Execute index operation in background."""
    try:
        # Update status to running
        _operations[operation_id]["status"] = "running"

        # Execute indexing
        job_id = indexer.run_indexing(
            root_directories=request.root_directories,
            job_id=request.job_id,
            resume=request.resume,
            force_reindex=request.force_reindex
        )

        # Get job summary for results
        summary = indexer.get_indexing_summary(job_id)
        job_stats = summary["summary"]

        # Update operation with results
        _operations[operation_id].update({
            "status": "completed",
            "progress": {
                "processed": job_stats["processed_files"],
                "total": job_stats["total_files"],
                "percentage": 100.0,
                "eta_seconds": 0,
                "current_item": None
            },
            "results": {
                "successful": job_stats["successful_files"],
                "failed": job_stats["failed_files"],
                "still_anomalous": job_stats["anomalous_files"]
            },
            "job_id": job_id
        })

        logger.info(f"Completed index operation {operation_id}")

    except Exception as e:
        logger.error(f"Index operation {operation_id} failed: {e}")
        _operations[operation_id]["status"] = "failed"


async def _execute_reindex_file_operation(
    operation_id: str,
    request: ReindexFileRequest,
    indexer: EbookIndexer,
    book_repo: BookRepository
):
    """Execute single file reindex operation in background."""
    try:
        # Update status to running
        _operations[operation_id]["status"] = "running"
        _operations[operation_id]["progress"]["current_item"] = request.file_path

        # If force is False, check if file was recently processed
        if not request.force:
            existing_book = book_repo.get_book_by_file_path(request.file_path)
            if existing_book and existing_book.processing.status.value == "completed":
                # File was already processed successfully, skip unless forced
                _operations[operation_id].update({
                    "status": "completed",
                    "progress": {
                        "processed": 1,
                        "total": 1,
                        "percentage": 100.0,
                        "eta_seconds": 0,
                        "current_item": request.file_path
                    },
                    "results": {
                        "successful": 0,  # Not reprocessed
                        "failed": 0,
                        "still_anomalous": len(existing_book.anomalies) if existing_book.anomalies else 0
                    }
                })
                logger.info(f"File {request.file_path} already processed, skipping (use force=true to reprocess)")
                return

        # Remove existing book document if it exists (for clean reprocessing)
        existing_book = book_repo.get_book_by_file_path(request.file_path)
        if existing_book:
            book_repo.delete_book(existing_book.id)
            logger.info(f"Removed existing book document for {request.file_path}")

        # Create a temporary job for this single file operation
        from ebook_indexer.database.models import ProcessingJob, JobStatus, JobStatistics, JobTiming
        temp_job = ProcessingJob(
            root_directories=[str(Path(request.file_path).parent)],
            status=JobStatus.RUNNING,
            statistics=JobStatistics(),
            timing=JobTiming(start_time=datetime.utcnow())
        )

        # Process the single file using the indexer's internal method
        from ebook_indexer.core.scanner import FileDiscovery
        from ebook_indexer.utils.file_utils import FileUtils

        # Create FileDiscovery object for the file
        file_path = Path(request.file_path)
        file_stats = file_path.stat()

        file_discovery = FileDiscovery(
            file_path=str(file_path),
            directory_path=str(file_path.parent),
            filename=file_path.name,
            file_extension=file_path.suffix.lower(),
            file_size=file_stats.st_size,
            last_modified=datetime.fromtimestamp(file_stats.st_mtime),
            is_supported=True,  # Already validated
            relative_path=file_path.name,
            nesting_level=1,
            collection_name=None,  # Will be determined during processing
            book_directory=None
        )

        # Process the file
        book_doc, has_anomalies = indexer._process_file(file_discovery, str(temp_job.id))

        # Save the processed book
        book_repo.save_book(book_doc)

        # Update operation with results
        _operations[operation_id].update({
            "status": "completed",
            "progress": {
                "processed": 1,
                "total": 1,
                "percentage": 100.0,
                "eta_seconds": 0,
                "current_item": request.file_path
            },
            "results": {
                "successful": 1,
                "failed": 0,
                "still_anomalous": 1 if has_anomalies else 0
            }
        })

        logger.info(f"Completed reindex file operation {operation_id} for {request.file_path}")

    except Exception as e:
        logger.error(f"Reindex file operation {operation_id} failed: {e}")
        _operations[operation_id].update({
            "status": "failed",
            "progress": {
                "processed": 0,
                "total": 1,
                "percentage": 0.0,
                "eta_seconds": None,
                "current_item": request.file_path
            },
            "results": {
                "successful": 0,
                "failed": 1,
                "still_anomalous": 0
            }
        })
