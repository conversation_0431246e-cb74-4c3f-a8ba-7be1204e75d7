"""API request models."""

from pydantic import BaseModel, Field, validator
from typing import List, Optional, Dict, Any
from enum import Enum


class JobCreateRequest(BaseModel):
    """Request to create a new indexing job."""
    root_directories: List[str] = Field(..., min_items=1, description="List of root directories to scan")
    resume: bool = Field(default=False, description="Whether to resume an existing job")
    job_id: Optional[str] = Field(default=None, description="Existing job ID to resume")
    force_reindex: bool = Field(default=False, description="Force reindexing of already indexed directories")

    @validator('root_directories')
    def validate_directories(cls, v):
        """Validate directory paths."""
        if not v:
            raise ValueError("At least one root directory must be provided")

        # Remove duplicates while preserving order
        seen = set()
        unique_dirs = []
        for directory in v:
            if directory not in seen:
                seen.add(directory)
                unique_dirs.append(directory)

        return unique_dirs


class RetryOperationRequest(BaseModel):
    """Request to retry failed files."""
    job_id: Optional[str] = Field(default=None, description="Specific job ID to retry")
    filter: Optional[Dict[str, Any]] = Field(default=None, description="Filter criteria for retry")

    class Config:
        schema_extra = {
            "example": {
                "job_id": "507f1f77bcf86cd799439011",
                "filter": {
                    "severity": ["high", "medium"],
                    "types": ["misplaced_file", "missing_directory"]
                }
            }
        }


class ReindexFileRequest(BaseModel):
    """Request to re-index a single file."""
    file_path: str = Field(..., description="Full path to the file to re-index")
    force: bool = Field(default=True, description="Force re-indexing even if file was recently processed")

    class Config:
        schema_extra = {
            "example": {
                "file_path": "/home/<USER>/ebooks/collection/book/file.pdf",
                "force": True
            }
        }


class AnomalyResolveRequest(BaseModel):
    """Request to resolve an anomaly."""
    resolved: bool = Field(..., description="Whether the anomaly is resolved")
    resolution_notes: Optional[str] = Field(default=None, description="Notes about the resolution")

    class Config:
        schema_extra = {
            "example": {
                "resolved": True,
                "resolution_notes": "Manually moved file to correct location"
            }
        }


class BulkAnomalyResolveRequest(BaseModel):
    """Request to resolve multiple anomalies."""
    anomaly_ids: List[str] = Field(..., min_items=1, description="List of anomaly IDs to resolve")
    resolved: bool = Field(..., description="Whether the anomalies are resolved")
    resolution_notes: Optional[str] = Field(default=None, description="Notes about the resolution")


class SearchRequest(BaseModel):
    """Search request for books."""
    query: str = Field(..., min_length=1, description="Search query")
    fields: Optional[List[str]] = Field(default=None, description="Fields to search in")
    filters: Optional[Dict[str, Any]] = Field(default=None, description="Additional filters")

    @validator('fields')
    def validate_fields(cls, v):
        """Validate search fields."""
        if v is not None:
            allowed_fields = ['title', 'author', 'publisher', 'isbn', 'description', 'filename']
            invalid_fields = [field for field in v if field not in allowed_fields]
            if invalid_fields:
                raise ValueError(f"Invalid search fields: {invalid_fields}")
        return v

    class Config:
        schema_extra = {
            "example": {
                "query": "programming python",
                "fields": ["title", "author", "description"],
                "filters": {
                    "format": "PDF",
                    "has_anomalies": False
                }
            }
        }


class ConfigUpdateRequest(BaseModel):
    """Request to update configuration."""
    anomaly_detection: Optional[Dict[str, Any]] = Field(default=None, description="Anomaly detection settings")
    supported_extensions: Optional[List[str]] = Field(default=None, description="Supported file extensions")
    batch_size: Optional[int] = Field(default=None, ge=1, le=1000, description="Processing batch size")

    class Config:
        schema_extra = {
            "example": {
                "anomaly_detection": {
                    "max_nesting_level": 3,
                    "naming_violations": {
                        "enabled": True,
                        "severity": "medium"
                    }
                },
                "supported_extensions": [".pdf", ".epub", ".mobi", ".txt"],
                "batch_size": 50
            }
        }


class ExportRequest(BaseModel):
    """Request to export data."""
    format: str = Field(..., description="Export format (json, csv, xlsx)")
    job_id: Optional[str] = Field(default=None, description="Specific job ID to export")
    include_anomalies: bool = Field(default=True, description="Include anomaly information")
    filters: Optional[Dict[str, Any]] = Field(default=None, description="Export filters")

    @validator('format')
    def validate_format(cls, v):
        """Validate export format."""
        allowed_formats = ['json', 'csv', 'xlsx']
        if v.lower() not in allowed_formats:
            raise ValueError(f"Format must be one of: {allowed_formats}")
        return v.lower()

    class Config:
        schema_extra = {
            "example": {
                "format": "json",
                "job_id": "507f1f77bcf86cd799439011",
                "include_anomalies": True,
                "filters": {
                    "has_anomalies": True,
                    "severity": ["high", "medium"]
                }
            }
        }


class WebSocketSubscribeRequest(BaseModel):
    """WebSocket subscription request."""
    topics: List[str] = Field(..., description="Topics to subscribe to")
    job_id: Optional[str] = Field(default=None, description="Specific job ID for job-specific updates")

    @validator('topics')
    def validate_topics(cls, v):
        """Validate subscription topics."""
        allowed_topics = ['jobs', 'operations', 'dashboard', 'anomalies']
        invalid_topics = [topic for topic in v if topic not in allowed_topics]
        if invalid_topics:
            raise ValueError(f"Invalid topics: {invalid_topics}")
        return v

    class Config:
        schema_extra = {
            "example": {
                "topics": ["jobs", "dashboard"],
                "job_id": "507f1f77bcf86cd799439011"
            }
        }


# Enum classes for validation
class JobStatusEnum(str, Enum):
    """Job status enumeration."""
    PENDING = "pending"
    RUNNING = "running"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"


class ProcessingStatusEnum(str, Enum):
    """Processing status enumeration."""
    PENDING = "pending"
    PROCESSING = "processing"
    COMPLETED = "completed"
    ERROR = "error"


class AnomalySeverityEnum(str, Enum):
    """Anomaly severity enumeration."""
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"


class AnomalyTypeEnum(str, Enum):
    """Anomaly type enumeration."""
    MISPLACED_FILE = "misplaced_file"
    MISSING_DIRECTORY = "missing_directory"
    DEEP_NESTING = "deep_nesting"
    NAMING_VIOLATION = "naming_violation"


class OperationTypeEnum(str, Enum):
    """Operation type enumeration."""
    INDEX = "index"
    RETRY = "retry"
    EXPORT = "export"
    CLEANUP = "cleanup"


class OperationStatusEnum(str, Enum):
    """Operation status enumeration."""
    PENDING = "pending"
    RUNNING = "running"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"
