#!/usr/bin/env python3
"""
Test script for the reindex-file endpoint.
"""

import requests
import json
import sys
import os
from pathlib import Path

def test_reindex_endpoint():
    """Test the reindex-file endpoint."""
    
    # API base URL
    base_url = "http://localhost:8000"
    endpoint = f"{base_url}/api/v1/operations/reindex-file"
    
    print("Testing reindex-file endpoint...")
    print(f"Endpoint: {endpoint}")
    
    # Test 1: Check if endpoint exists with OPTIONS request
    print("\n1. Testing OPTIONS request (CORS preflight)...")
    try:
        response = requests.options(endpoint)
        print(f"OPTIONS Status: {response.status_code}")
        print(f"Allowed methods: {response.headers.get('Allow', 'Not specified')}")
        print(f"CORS headers: {response.headers.get('Access-Control-Allow-Methods', 'Not specified')}")
    except Exception as e:
        print(f"OPTIONS request failed: {e}")
    
    # Test 2: Test with a non-existent file (should return 404)
    print("\n2. Testing POST with non-existent file...")
    test_data = {
        "file_path": "/non/existent/file.pdf",
        "force": True
    }
    
    try:
        response = requests.post(
            endpoint,
            json=test_data,
            headers={"Content-Type": "application/json"}
        )
        print(f"POST Status: {response.status_code}")
        print(f"Response: {response.text}")
    except Exception as e:
        print(f"POST request failed: {e}")
    
    # Test 3: Test with this script file (should work if it's a supported extension)
    print("\n3. Testing POST with this script file...")
    script_path = os.path.abspath(__file__)
    test_data = {
        "file_path": script_path,
        "force": True
    }
    
    try:
        response = requests.post(
            endpoint,
            json=test_data,
            headers={"Content-Type": "application/json"}
        )
        print(f"POST Status: {response.status_code}")
        print(f"Response: {response.text}")
    except Exception as e:
        print(f"POST request failed: {e}")
    
    # Test 4: Check if the API is running at all
    print("\n4. Testing API health...")
    try:
        health_response = requests.get(f"{base_url}/api/health")
        print(f"Health Status: {health_response.status_code}")
        if health_response.status_code == 200:
            health_data = health_response.json()
            print(f"API Status: {health_data.get('status', 'unknown')}")
            print(f"Mode: {health_data.get('mode', 'unknown')}")
        else:
            print(f"Health Response: {health_response.text}")
    except Exception as e:
        print(f"Health check failed: {e}")
    
    # Test 5: List available operations endpoints
    print("\n5. Testing operations list endpoint...")
    try:
        ops_response = requests.get(f"{base_url}/api/v1/operations")
        print(f"Operations Status: {ops_response.status_code}")
        if ops_response.status_code == 200:
            ops_data = ops_response.json()
            print(f"Operations count: {len(ops_data)}")
        else:
            print(f"Operations Response: {ops_response.text}")
    except Exception as e:
        print(f"Operations request failed: {e}")

def test_frontend_api_call():
    """Test the exact API call that the frontend is making."""
    print("\n" + "="*60)
    print("TESTING FRONTEND API CALL")
    print("="*60)
    
    # This simulates the exact call from the frontend
    base_url = "http://localhost:8000"
    endpoint = f"{base_url}/api/v1/operations/reindex-file"
    
    # Test file path (use this script)
    test_file_path = os.path.abspath(__file__)
    
    payload = {
        "file_path": test_file_path,
        "force": True
    }
    
    headers = {
        "Content-Type": "application/json",
        "Accept": "application/json"
    }
    
    print(f"URL: {endpoint}")
    print(f"Method: POST")
    print(f"Headers: {headers}")
    print(f"Payload: {json.dumps(payload, indent=2)}")
    
    try:
        response = requests.post(endpoint, json=payload, headers=headers)
        print(f"\nResponse Status: {response.status_code}")
        print(f"Response Headers: {dict(response.headers)}")
        print(f"Response Body: {response.text}")
        
        if response.status_code == 405:
            print("\n❌ Method Not Allowed - This suggests the endpoint doesn't accept POST")
            print("Possible causes:")
            print("1. Endpoint not properly registered")
            print("2. Router not included in main app")
            print("3. Method decorator issue")
            
    except Exception as e:
        print(f"\n❌ Request failed: {e}")

if __name__ == "__main__":
    print("REINDEX ENDPOINT TEST")
    print("=" * 60)
    
    test_reindex_endpoint()
    test_frontend_api_call()
    
    print("\n" + "=" * 60)
    print("TEST COMPLETE")
    print("=" * 60)
    
    print("\nIf you see 'Method Not Allowed', check:")
    print("1. FastAPI server is running: uvicorn api.main:app --reload")
    print("2. Operations router is properly included")
    print("3. Endpoint decorator is correct (@router.post)")
    print("4. No conflicting routes")
