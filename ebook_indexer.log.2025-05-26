2025-05-26 00:02:38 - ebook_indexer.utils.progress_tracker.ProgressTracker - INFO - Completed: Indexing 1 directories - 345/345 items (100.0%) in 0:04:02.331955 (1.42 items/sec)
2025-05-26 00:02:54 - ebook_indexer.core.indexer.EbookIndexer - INFO - Job 68339285929e54069b3bccec completed: 364/364 files successful, 0 failed, 22 with anomalies
2025-05-26 00:04:15 - ebook_indexer.database.connection.MongoDBConnection - INFO - Connecting to MongoDB at mongodb://localhost:27017
2025-05-26 00:04:15 - ebook_indexer.database.connection.MongoDBConnection - INFO - Successfully connected to database 'ebook_indexer'
2025-05-26 00:05:07 - ebook_indexer.database.connection.MongoDBConnection - INFO - Disconnecting from MongoDB
2025-05-26 00:06:11 - ebook_indexer.database.connection.MongoDBConnection - INFO - Connecting to MongoDB at mongodb://localhost:27017
2025-05-26 00:06:11 - ebook_indexer.database.connection.MongoDBConnection - INFO - Successfully connected to database 'ebook_indexer'
2025-05-26 00:09:19 - ebook_indexer.database.connection.MongoDBConnection - INFO - Disconnecting from MongoDB
2025-05-26 00:09:26 - ebook_indexer.database.connection.MongoDBConnection - INFO - Connecting to MongoDB at mongodb://localhost:27017
2025-05-26 00:09:26 - ebook_indexer.database.connection.MongoDBConnection - INFO - Successfully connected to database 'ebook_indexer'
2025-05-26 00:09:48 - ebook_indexer.core.scanner.DirectoryScanner - INFO - Scanner initialized with extensions: ['.pdf', '.epub', '.mobi', '.azw', '.azw3']
2025-05-26 00:09:48 - ebook_indexer.core.metadata_extractor.MetadataExtractor - INFO - Metadata extractor initialized for formats: ['.pdf', '.epub']
2025-05-26 00:09:48 - ebook_indexer.core.anomaly_detector.AnomalyDetector - INFO - Anomaly detector initialized with config: {'max_nesting_depth': 4, 'enforce_naming_convention': True, 'detect_misplaced_files': True, 'severity_thresholds': {'wrong_level': 'medium', 'deep_nesting': 'low', 'missing_directory': 'high', 'naming_violation': 'low'}}
2025-05-26 00:09:48 - ebook_indexer.core.indexer.EbookIndexer - INFO - Ebook indexer initialized
2025-05-26 00:10:28 - ebook_indexer.core.scanner.DirectoryScanner - INFO - Scanner initialized with extensions: ['.pdf', '.epub', '.mobi', '.azw', '.azw3']
2025-05-26 00:10:28 - ebook_indexer.core.metadata_extractor.MetadataExtractor - INFO - Metadata extractor initialized for formats: ['.pdf', '.epub']
2025-05-26 00:10:28 - ebook_indexer.core.anomaly_detector.AnomalyDetector - INFO - Anomaly detector initialized with config: {'max_nesting_depth': 4, 'enforce_naming_convention': True, 'detect_misplaced_files': True, 'severity_thresholds': {'wrong_level': 'medium', 'deep_nesting': 'low', 'missing_directory': 'high', 'naming_violation': 'low'}}
2025-05-26 00:10:28 - ebook_indexer.core.indexer.EbookIndexer - INFO - Ebook indexer initialized
2025-05-26 00:15:32 - ebook_indexer.database.connection.MongoDBConnection - INFO - Connecting to MongoDB at mongodb://localhost:27017
2025-05-26 00:15:32 - ebook_indexer.database.connection.MongoDBConnection - INFO - Successfully connected to database 'ebook_indexer'
2025-05-26 00:15:39 - ebook_indexer.database.connection.MongoDBConnection - INFO - Connecting to MongoDB at mongodb://localhost:27017
2025-05-26 00:15:39 - ebook_indexer.database.connection.MongoDBConnection - INFO - Successfully connected to database 'ebook_indexer'
2025-05-26 00:15:57 - ebook_indexer.database.connection.MongoDBConnection - INFO - Connecting to MongoDB at mongodb://localhost:27017
2025-05-26 00:15:57 - ebook_indexer.database.connection.MongoDBConnection - INFO - Successfully connected to database 'ebook_indexer'
2025-05-26 00:18:06 - ebook_indexer.database.connection.MongoDBConnection - INFO - Connecting to MongoDB at mongodb://localhost:27017
2025-05-26 00:18:06 - ebook_indexer.database.connection.MongoDBConnection - INFO - Successfully connected to database 'ebook_indexer'
2025-05-26 00:19:44 - ebook_indexer.database.connection.MongoDBConnection - INFO - Connecting to MongoDB at mongodb://localhost:27017
2025-05-26 00:19:44 - ebook_indexer.database.connection.MongoDBConnection - INFO - Successfully connected to database 'ebook_indexer'
2025-05-26 00:20:11 - ebook_indexer.database.connection.MongoDBConnection - INFO - Connecting to MongoDB at mongodb://localhost:27017
2025-05-26 00:20:11 - ebook_indexer.database.connection.MongoDBConnection - INFO - Successfully connected to database 'ebook_indexer'
2025-05-26 00:20:16 - ebook_indexer.database.connection.MongoDBConnection - INFO - Connecting to MongoDB at mongodb://localhost:27017
2025-05-26 00:20:16 - ebook_indexer.database.connection.MongoDBConnection - INFO - Successfully connected to database 'ebook_indexer'
2025-05-26 00:20:44 - ebook_indexer.database.connection.MongoDBConnection - INFO - Connecting to MongoDB at mongodb://localhost:27017
2025-05-26 00:20:44 - ebook_indexer.database.connection.MongoDBConnection - INFO - Successfully connected to database 'ebook_indexer'
2025-05-26 00:22:32 - ebook_indexer.database.connection.MongoDBConnection - INFO - Connecting to MongoDB at mongodb://localhost:27017
2025-05-26 00:22:32 - ebook_indexer.database.connection.MongoDBConnection - INFO - Successfully connected to database 'ebook_indexer'
2025-05-26 00:30:45 - ebook_indexer.database.connection.MongoDBConnection - INFO - Connecting to MongoDB at mongodb://localhost:27017
2025-05-26 00:30:45 - ebook_indexer.database.connection.MongoDBConnection - INFO - Successfully connected to database 'ebook_indexer'
2025-05-26 00:49:09 - ebook_indexer.database.connection.MongoDBConnection - INFO - Connecting to MongoDB at mongodb://localhost:27017
2025-05-26 00:49:09 - ebook_indexer.database.connection.MongoDBConnection - INFO - Successfully connected to database 'ebook_indexer'
2025-05-26 00:51:15 - ebook_indexer.database.connection.MongoDBConnection - INFO - Disconnecting from MongoDB
2025-05-26 21:09:09 - ebook_indexer.database.connection.MongoDBConnection - INFO - Connecting to MongoDB at mongodb://localhost:27017
2025-05-26 21:09:14 - ebook_indexer.database.connection.MongoDBConnection - ERROR - Failed to connect to MongoDB: localhost:27017: [Errno 111] Connection refused (configured timeouts: socketTimeoutMS: 5000.0ms, connectTimeoutMS: 5000.0ms), Timeout: 5.0s, Topology Description: <TopologyDescription id: 6834bc555b0449ed1c873169, topology_type: Unknown, servers: [<ServerDescription ('localhost', 27017) server_type: Unknown, rtt: None, error=AutoReconnect('localhost:27017: [Errno 111] Connection refused (configured timeouts: socketTimeoutMS: 5000.0ms, connectTimeoutMS: 5000.0ms)')>]>
2025-05-26 21:09:14 - ebook_indexer.database.connection.MongoDBConnection - WARNING - MongoDB health check failed: Failed to connect to MongoDB: localhost:27017: [Errno 111] Connection refused (configured timeouts: socketTimeoutMS: 5000.0ms, connectTimeoutMS: 5000.0ms), Timeout: 5.0s, Topology Description: <TopologyDescription id: 6834bc555b0449ed1c873169, topology_type: Unknown, servers: [<ServerDescription ('localhost', 27017) server_type: Unknown, rtt: None, error=AutoReconnect('localhost:27017: [Errno 111] Connection refused (configured timeouts: socketTimeoutMS: 5000.0ms, connectTimeoutMS: 5000.0ms)')>]>
2025-05-26 21:23:12 - ebook_indexer.database.connection.MongoDBConnection - INFO - Connecting to MongoDB at mongodb://localhost:27017
2025-05-26 21:23:12 - ebook_indexer.database.connection.MongoDBConnection - INFO - Successfully connected to database 'ebook_indexer'
2025-05-26 21:25:48 - ebook_indexer.database.connection.MongoDBConnection - INFO - Connecting to MongoDB at mongodb://localhost:27017
2025-05-26 21:25:48 - ebook_indexer.database.connection.MongoDBConnection - INFO - Successfully connected to database 'ebook_indexer'
2025-05-26 21:26:14 - ebook_indexer.database.connection.MongoDBConnection - INFO - Connecting to MongoDB at mongodb://localhost:27017
2025-05-26 21:26:14 - ebook_indexer.database.connection.MongoDBConnection - INFO - Successfully connected to database 'ebook_indexer'
2025-05-26 21:26:31 - ebook_indexer.database.connection.MongoDBConnection - INFO - Connecting to MongoDB at mongodb://localhost:27017
2025-05-26 21:26:31 - ebook_indexer.database.connection.MongoDBConnection - INFO - Successfully connected to database 'ebook_indexer'
2025-05-26 21:26:31 - ebook_indexer.core.scanner.DirectoryScanner - INFO - Scanner initialized with extensions: ['.pdf', '.epub', '.mobi', '.azw', '.azw3', '.txt']
2025-05-26 21:26:31 - ebook_indexer.core.metadata_extractor.MetadataExtractor - INFO - Metadata extractor initialized for formats: ['.pdf', '.epub']
2025-05-26 21:26:31 - ebook_indexer.core.anomaly_detector.AnomalyDetector - INFO - Anomaly detector initialized with config: {'max_nesting_depth': 4, 'enforce_naming_convention': True, 'detect_misplaced_files': True, 'severity_thresholds': {'wrong_level': 'medium', 'deep_nesting': 'low', 'missing_directory': 'high', 'naming_violation': 'low'}}
2025-05-26 21:26:31 - ebook_indexer.core.indexer.EbookIndexer - INFO - Ebook indexer initialized
2025-05-26 21:26:31 - ebook_indexer.core.indexer.EbookIndexer - INFO - Created new job 6834c067c4f0a7b89a323d53
2025-05-26 21:26:31 - ebook_indexer.core.indexer.EbookIndexer - INFO - Starting indexing job 6834c067c4f0a7b89a323d53 for directories: ['/run/user/1000/gvfs/smb-share:server=**************,share=dmedia6/scanlibs-ebooks-202402', '/run/user/1000/gvfs/smb-share:server=**************,share=dmedia6/scanlibs-ebooks-202403']
2025-05-26 21:26:45 - ebook_indexer.core.indexer.EbookIndexer - INFO - Found 812 files to process
2025-05-26 21:26:45 - ebook_indexer.utils.progress_tracker.ProgressTracker - INFO - Progress tracker initialized: Indexing 2 directories (812 items)
2025-05-26 21:26:45 - ebook_indexer.core.scanner.DirectoryScanner - INFO - Scanning directory: /run/user/1000/gvfs/smb-share:server=**************,share=dmedia6/scanlibs-ebooks-202402
2025-05-26 21:26:45 - ebook_indexer.core.scanner.DirectoryScanner - INFO - Starting scan of directory: /run/user/1000/gvfs/smb-share:server=**************,share=dmedia6/scanlibs-ebooks-202402
2025-05-26 21:26:46 - ebook_indexer.utils.progress_tracker.ProgressTracker - INFO - Started: Indexing 2 directories
2025-05-26 21:26:46 - ebook_indexer.core.indexer.EbookIndexer - INFO - Indexing 2 directories: 0/812 (0.0%)
2025-05-26 21:27:14 - ebook_indexer.core.indexer.EbookIndexer - INFO - Indexing 2 directories: 50/812 (6.2%) - 1.80 items/sec - ETA: 7m 4s
2025-05-26 21:28:04 - ebook_indexer.core.indexer.EbookIndexer - WARNING - Metadata extraction failed for ruby-rails-tutorial-7th.epub: Error reading EPUB metadata: "There is no item named 'OEBPS/graphics/f0550-02.jpg' in the archive"
2025-05-26 21:31:07 - ebook_indexer.core.scanner.DirectoryScanner - INFO - Scanning directory: /run/user/1000/gvfs/smb-share:server=**************,share=dmedia6/scanlibs-ebooks-202403
2025-05-26 21:31:07 - ebook_indexer.core.scanner.DirectoryScanner - INFO - Starting scan of directory: /run/user/1000/gvfs/smb-share:server=**************,share=dmedia6/scanlibs-ebooks-202403
2025-05-26 21:33:30 - ebook_indexer.core.indexer.EbookIndexer - INFO - Indexing 2 directories: 600/812 (73.9%) - 1.49 items/sec - ETA: 2m 22s
2025-05-26 21:34:00 - ebook_indexer.core.indexer.EbookIndexer - INFO - Indexing 2 directories: 650/812 (80.0%) - 1.50 items/sec - ETA: 1m 48s
2025-05-26 21:35:01 - ebook_indexer.core.indexer.EbookIndexer - INFO - Indexing 2 directories: 750/812 (92.4%) - 1.52 items/sec - ETA: 0m 40s
2025-05-26 21:35:04 - ebook_indexer.core.indexer.EbookIndexer - INFO - Indexing 2 directories: 800/812 (98.5%) - 1.61 items/sec - ETA: 0m 7s
2025-05-26 21:35:12 - ebook_indexer.utils.progress_tracker.ProgressTracker - INFO - Completed: Indexing 2 directories - 812/812 items (100.0%) in 0:08:25.431744 (1.61 items/sec)
2025-05-26 21:35:58 - ebook_indexer.core.indexer.EbookIndexer - WARNING - Metadata extraction failed for mastering-microsoft-fabric-saasification.epub: Error reading EPUB metadata: 'Bad Zip file'
2025-05-26 21:36:07 - ebook_indexer.core.indexer.EbookIndexer - INFO - Indexing 2 directories: 900/812 (110.8%) - 1.61 items/sec
2025-05-26 21:36:14 - ebook_indexer.core.indexer.EbookIndexer - INFO - Job 6834c067c4f0a7b89a323d53 completed: 916/916 files successful, 0 failed, 107 with anomalies
2025-05-26 23:49:11 - ebook_indexer.database.connection.MongoDBConnection - INFO - Connecting to MongoDB at mongodb://localhost:27017
2025-05-26 23:49:11 - ebook_indexer.database.connection.MongoDBConnection - INFO - Successfully connected to database 'ebook_indexer'
2025-05-26 23:56:20 - ebook_indexer.database.connection.MongoDBConnection - INFO - Connecting to MongoDB at mongodb://localhost:27017
2025-05-26 23:56:20 - ebook_indexer.database.connection.MongoDBConnection - INFO - Successfully connected to database 'ebook_indexer'
2025-05-26 23:56:22 - ebook_indexer.core.scanner.DirectoryScanner - INFO - Scanner initialized with extensions: ['.pdf', '.epub', '.mobi', '.azw', '.azw3', '.txt']
2025-05-26 23:56:22 - ebook_indexer.core.metadata_extractor.MetadataExtractor - INFO - Metadata extractor initialized for formats: ['.pdf', '.epub']
2025-05-26 23:56:22 - ebook_indexer.core.anomaly_detector.AnomalyDetector - INFO - Anomaly detector initialized with config: {'max_nesting_depth': 4, 'enforce_naming_convention': True, 'detect_misplaced_files': True, 'severity_thresholds': {'wrong_level': 'medium', 'deep_nesting': 'low', 'missing_directory': 'high', 'naming_violation': 'low'}}
2025-05-26 23:56:22 - ebook_indexer.core.indexer.EbookIndexer - INFO - Ebook indexer initialized
2025-05-26 23:56:22 - ebook_indexer.core.indexer.EbookIndexer - INFO - Skipping 2 already indexed directories: ['/run/user/1000/gvfs/smb-share:server=**************,share=dmedia6/scanlibs-ebooks-202402', '/run/user/1000/gvfs/smb-share:server=**************,share=dmedia6/scanlibs-ebooks-202403']
2025-05-26 23:56:22 - ebook_indexer.core.indexer.EbookIndexer - INFO - Use --force flag to reindex these directories
2025-05-26 23:56:22 - ebook_indexer.core.indexer.EbookIndexer - INFO - All specified directories have already been indexed successfully
2025-05-26 23:56:22 - ebook_indexer.core.indexer.EbookIndexer - INFO - Use --force flag to reindex or check recent jobs with 'ebook-indexer stats'
2025-05-26 23:56:39 - ebook_indexer.database.connection.MongoDBConnection - INFO - Connecting to MongoDB at mongodb://localhost:27017
2025-05-26 23:56:39 - ebook_indexer.database.connection.MongoDBConnection - INFO - Successfully connected to database 'ebook_indexer'
2025-05-26 23:56:39 - ebook_indexer.core.scanner.DirectoryScanner - INFO - Scanner initialized with extensions: ['.pdf', '.epub', '.mobi', '.azw', '.azw3', '.txt']
2025-05-26 23:56:39 - ebook_indexer.core.metadata_extractor.MetadataExtractor - INFO - Metadata extractor initialized for formats: ['.pdf', '.epub']
2025-05-26 23:56:39 - ebook_indexer.core.anomaly_detector.AnomalyDetector - INFO - Anomaly detector initialized with config: {'max_nesting_depth': 4, 'enforce_naming_convention': True, 'detect_misplaced_files': True, 'severity_thresholds': {'wrong_level': 'medium', 'deep_nesting': 'low', 'missing_directory': 'high', 'naming_violation': 'low'}}
2025-05-26 23:56:39 - ebook_indexer.core.indexer.EbookIndexer - INFO - Ebook indexer initialized
2025-05-26 23:56:39 - ebook_indexer.core.indexer.EbookIndexer - INFO - Skipping 2 already indexed directories: ['/run/user/1000/gvfs/smb-share:server=**************,share=dmedia6/scanlibs-ebooks-202402', '/run/user/1000/gvfs/smb-share:server=**************,share=dmedia6/scanlibs-ebooks-202403']
2025-05-26 23:56:39 - ebook_indexer.core.indexer.EbookIndexer - INFO - Use --force flag to reindex these directories
2025-05-26 23:56:39 - ebook_indexer.core.indexer.EbookIndexer - INFO - Created new job 6834e3974148acbf2068928a
2025-05-26 23:56:39 - ebook_indexer.core.indexer.EbookIndexer - INFO - Starting indexing job 6834e3974148acbf2068928a for directories: ['/run/user/1000/gvfs/smb-share:server=**************,share=dmedia6/scanlibs-ebooks-202404']
2025-05-26 23:56:56 - ebook_indexer.core.indexer.EbookIndexer - INFO - Found 1083 files to process
2025-05-26 23:56:56 - ebook_indexer.utils.progress_tracker.ProgressTracker - INFO - Progress tracker initialized: Indexing 1 directories (skipped 2 already indexed) (1083 items)
2025-05-26 23:56:56 - ebook_indexer.core.scanner.DirectoryScanner - INFO - Scanning directory: /run/user/1000/gvfs/smb-share:server=**************,share=dmedia6/scanlibs-ebooks-202404
2025-05-26 23:56:56 - ebook_indexer.core.scanner.DirectoryScanner - INFO - Starting scan of directory: /run/user/1000/gvfs/smb-share:server=**************,share=dmedia6/scanlibs-ebooks-202404
2025-05-26 23:57:00 - ebook_indexer.utils.progress_tracker.ProgressTracker - INFO - Started: Indexing 1 directories (skipped 2 already indexed)
2025-05-26 23:57:00 - ebook_indexer.core.indexer.EbookIndexer - INFO - Indexing 1 directories (skipped 2 already indexed): 0/1083 (0.0%)
2025-05-26 23:58:24 - ebook_indexer.core.indexer.EbookIndexer - WARNING - Metadata extraction failed for Synthetic.Data.and.Generative.AI.0443218579.epub: Error reading EPUB metadata: "There is no item named 'OEBPS/images/B9780443218576000114/si144.png' in the archive"
2025-05-26 23:58:31 - ebook_indexer.core.indexer.EbookIndexer - INFO - Indexing 1 directories (skipped 2 already indexed): 100/1083 (9.2%) - 1.10 items/sec - ETA: 14m 51s
2025-05-27 00:33:21 - ebook_indexer.database.connection.MongoDBConnection - INFO - Disconnecting from MongoDB
