2025-05-27 00:00:04 - ebook_indexer.core.indexer.EbookIndexer - INFO - Indexing 1 directories (skipped 2 already indexed): 250/1083 (23.1%) - 1.36 items/sec - ETA: 10m 12s
2025-05-27 00:01:17 - ebook_indexer.core.indexer.EbookIndexer - INFO - Indexing 1 directories (skipped 2 already indexed): 350/1083 (32.3%) - 1.36 items/sec - ETA: 8m 57s
2025-05-27 00:01:42 - ebook_indexer.core.indexer.EbookIndexer - INFO - Indexing 1 directories (skipped 2 already indexed): 400/1083 (36.9%) - 1.42 items/sec - ETA: 8m 1s
2025-05-27 00:02:46 - ebook_indexer.core.indexer.EbookIndexer - INFO - Indexing 1 directories (skipped 2 already indexed): 500/1083 (46.2%) - 1.45 items/sec - ETA: 6m 43s
2025-05-27 00:02:51 - ebook_indexer.core.indexer.EbookIndexer - WARNING - Metadata extraction failed for technical-agile-coaching-samman-method.epub: Error reading EPUB metadata: "There is no item named 'OEBPS/images/leanpub_key.png' in the archive"
2025-05-27 00:03:11 - ebook_indexer.core.indexer.EbookIndexer - INFO - Indexing 1 directories (skipped 2 already indexed): 550/1083 (50.8%) - 1.48 items/sec - ETA: 5m 59s
2025-05-27 00:04:05 - ebook_indexer.core.indexer.EbookIndexer - INFO - Indexing 1 directories (skipped 2 already indexed): 650/1083 (60.0%) - 1.53 items/sec - ETA: 4m 43s
2025-05-27 00:05:28 - ebook_indexer.core.indexer.EbookIndexer - WARNING - Metadata extraction failed for engineering-circuit-analysis-10th.epub: Error reading EPUB metadata: "There is no item named 'OPS/toc.ncx' in the archive"
2025-05-27 00:07:16 - ebook_indexer.core.indexer.EbookIndexer - INFO - Indexing 1 directories (skipped 2 already indexed): 950/1083 (87.7%) - 1.54 items/sec - ETA: 1m 26s
2025-05-27 00:07:31 - ebook_indexer.core.indexer.EbookIndexer - WARNING - Metadata extraction failed for mastering-microsoft-fabric-saasification.epub: Error reading EPUB metadata: 'Bad Zip file'
2025-05-27 00:07:48 - ebook_indexer.core.indexer.EbookIndexer - INFO - Indexing 1 directories (skipped 2 already indexed): 1000/1083 (92.3%) - 1.54 items/sec - ETA: 0m 53s
2025-05-27 00:08:36 - ebook_indexer.core.indexer.EbookIndexer - WARNING - Metadata extraction failed for synthetic-data-generative-ai.epub: Error reading EPUB metadata: "There is no item named 'OEBPS/images/B9780443218576000114/si144.png' in the archive"
2025-05-27 00:08:38 - ebook_indexer.core.indexer.EbookIndexer - WARNING - Metadata extraction failed for theory-structured-parallel-programming.epub: Error reading EPUB metadata: "There is no item named 'OEBPS/images/B9780443248146000095/fx002.jpg' in the archive"
2025-05-27 00:08:45 - ebook_indexer.utils.progress_tracker.ProgressTracker - INFO - Completed: Indexing 1 directories (skipped 2 already indexed) - 1083/1083 items (100.0%) in 0:11:45.191234 (1.54 items/sec)
2025-05-27 00:08:47 - ebook_indexer.core.indexer.EbookIndexer - INFO - Job 6834e3974148acbf2068928a completed: 1085/1085 files successful, 0 failed, 3 with anomalies
2025-05-27 00:34:11 - ebook_indexer.database.connection.MongoDBConnection - INFO - Connecting to MongoDB at mongodb://localhost:27017
2025-05-27 00:34:11 - ebook_indexer.database.connection.MongoDBConnection - INFO - Successfully connected to database 'ebook_indexer'
2025-05-27 00:34:23 - ebook_indexer.core.scanner.DirectoryScanner - INFO - Scanner initialized with extensions: ['.pdf', '.epub', '.mobi', '.azw', '.azw3']
2025-05-27 00:34:23 - ebook_indexer.core.metadata_extractor.MetadataExtractor - INFO - Metadata extractor initialized for formats: ['.pdf', '.epub']
2025-05-27 00:34:23 - ebook_indexer.core.anomaly_detector.AnomalyDetector - INFO - Anomaly detector initialized with config: {'max_nesting_depth': 4, 'enforce_naming_convention': True, 'detect_misplaced_files': True, 'severity_thresholds': {'wrong_level': 'medium', 'deep_nesting': 'low', 'missing_directory': 'high', 'naming_violation': 'low'}}
2025-05-27 00:34:23 - ebook_indexer.core.indexer.EbookIndexer - INFO - Ebook indexer initialized
2025-05-27 00:34:23 - ebook_indexer.core.scanner.DirectoryScanner - INFO - Scanner initialized with extensions: ['.pdf', '.epub', '.mobi', '.azw', '.azw3']
2025-05-27 00:34:23 - ebook_indexer.core.metadata_extractor.MetadataExtractor - INFO - Metadata extractor initialized for formats: ['.pdf', '.epub']
2025-05-27 00:34:23 - ebook_indexer.core.anomaly_detector.AnomalyDetector - INFO - Anomaly detector initialized with config: {'max_nesting_depth': 4, 'enforce_naming_convention': True, 'detect_misplaced_files': True, 'severity_thresholds': {'wrong_level': 'medium', 'deep_nesting': 'low', 'missing_directory': 'high', 'naming_violation': 'low'}}
2025-05-27 00:34:23 - ebook_indexer.core.indexer.EbookIndexer - INFO - Ebook indexer initialized
2025-05-27 00:34:23 - ebook_indexer.core.scanner.DirectoryScanner - INFO - Scanner initialized with extensions: ['.pdf', '.epub', '.mobi', '.azw', '.azw3']
2025-05-27 00:34:23 - ebook_indexer.core.metadata_extractor.MetadataExtractor - INFO - Metadata extractor initialized for formats: ['.pdf', '.epub']
2025-05-27 00:34:23 - ebook_indexer.core.anomaly_detector.AnomalyDetector - INFO - Anomaly detector initialized with config: {'max_nesting_depth': 4, 'enforce_naming_convention': True, 'detect_misplaced_files': True, 'severity_thresholds': {'wrong_level': 'medium', 'deep_nesting': 'low', 'missing_directory': 'high', 'naming_violation': 'low'}}
2025-05-27 00:34:23 - ebook_indexer.core.indexer.EbookIndexer - INFO - Ebook indexer initialized
2025-05-27 00:34:45 - ebook_indexer.core.scanner.DirectoryScanner - INFO - Scanner initialized with extensions: ['.pdf', '.epub', '.mobi', '.azw', '.azw3']
2025-05-27 00:34:45 - ebook_indexer.core.metadata_extractor.MetadataExtractor - INFO - Metadata extractor initialized for formats: ['.pdf', '.epub']
2025-05-27 00:34:45 - ebook_indexer.core.anomaly_detector.AnomalyDetector - INFO - Anomaly detector initialized with config: {'max_nesting_depth': 4, 'enforce_naming_convention': True, 'detect_misplaced_files': True, 'severity_thresholds': {'wrong_level': 'medium', 'deep_nesting': 'low', 'missing_directory': 'high', 'naming_violation': 'low'}}
2025-05-27 00:34:45 - ebook_indexer.core.indexer.EbookIndexer - INFO - Ebook indexer initialized
2025-05-27 00:34:51 - ebook_indexer.core.scanner.DirectoryScanner - INFO - Scanner initialized with extensions: ['.pdf', '.epub', '.mobi', '.azw', '.azw3']
2025-05-27 00:34:51 - ebook_indexer.core.metadata_extractor.MetadataExtractor - INFO - Metadata extractor initialized for formats: ['.pdf', '.epub']
2025-05-27 00:34:51 - ebook_indexer.core.anomaly_detector.AnomalyDetector - INFO - Anomaly detector initialized with config: {'max_nesting_depth': 4, 'enforce_naming_convention': True, 'detect_misplaced_files': True, 'severity_thresholds': {'wrong_level': 'medium', 'deep_nesting': 'low', 'missing_directory': 'high', 'naming_violation': 'low'}}
2025-05-27 00:34:51 - ebook_indexer.core.indexer.EbookIndexer - INFO - Ebook indexer initialized
2025-05-27 00:35:41 - ebook_indexer.core.scanner.DirectoryScanner - INFO - Scanner initialized with extensions: ['.pdf', '.epub', '.mobi', '.azw', '.azw3']
2025-05-27 00:35:41 - ebook_indexer.core.metadata_extractor.MetadataExtractor - INFO - Metadata extractor initialized for formats: ['.pdf', '.epub']
2025-05-27 00:35:41 - ebook_indexer.core.anomaly_detector.AnomalyDetector - INFO - Anomaly detector initialized with config: {'max_nesting_depth': 4, 'enforce_naming_convention': True, 'detect_misplaced_files': True, 'severity_thresholds': {'wrong_level': 'medium', 'deep_nesting': 'low', 'missing_directory': 'high', 'naming_violation': 'low'}}
2025-05-27 00:35:41 - ebook_indexer.core.indexer.EbookIndexer - INFO - Ebook indexer initialized
2025-05-27 00:35:44 - ebook_indexer.core.scanner.DirectoryScanner - INFO - Scanner initialized with extensions: ['.pdf', '.epub', '.mobi', '.azw', '.azw3']
2025-05-27 00:35:44 - ebook_indexer.core.metadata_extractor.MetadataExtractor - INFO - Metadata extractor initialized for formats: ['.pdf', '.epub']
2025-05-27 00:35:44 - ebook_indexer.core.anomaly_detector.AnomalyDetector - INFO - Anomaly detector initialized with config: {'max_nesting_depth': 4, 'enforce_naming_convention': True, 'detect_misplaced_files': True, 'severity_thresholds': {'wrong_level': 'medium', 'deep_nesting': 'low', 'missing_directory': 'high', 'naming_violation': 'low'}}
2025-05-27 00:35:44 - ebook_indexer.core.indexer.EbookIndexer - INFO - Ebook indexer initialized
2025-05-27 00:36:07 - ebook_indexer.core.scanner.DirectoryScanner - INFO - Scanner initialized with extensions: ['.pdf', '.epub', '.mobi', '.azw', '.azw3']
2025-05-27 00:36:07 - ebook_indexer.core.metadata_extractor.MetadataExtractor - INFO - Metadata extractor initialized for formats: ['.pdf', '.epub']
2025-05-27 00:36:07 - ebook_indexer.core.anomaly_detector.AnomalyDetector - INFO - Anomaly detector initialized with config: {'max_nesting_depth': 4, 'enforce_naming_convention': True, 'detect_misplaced_files': True, 'severity_thresholds': {'wrong_level': 'medium', 'deep_nesting': 'low', 'missing_directory': 'high', 'naming_violation': 'low'}}
2025-05-27 00:36:07 - ebook_indexer.core.indexer.EbookIndexer - INFO - Ebook indexer initialized
2025-05-27 00:36:25 - ebook_indexer.core.scanner.DirectoryScanner - INFO - Scanner initialized with extensions: ['.pdf', '.epub', '.mobi', '.azw', '.azw3']
2025-05-27 00:36:25 - ebook_indexer.core.metadata_extractor.MetadataExtractor - INFO - Metadata extractor initialized for formats: ['.pdf', '.epub']
2025-05-27 00:36:25 - ebook_indexer.core.anomaly_detector.AnomalyDetector - INFO - Anomaly detector initialized with config: {'max_nesting_depth': 4, 'enforce_naming_convention': True, 'detect_misplaced_files': True, 'severity_thresholds': {'wrong_level': 'medium', 'deep_nesting': 'low', 'missing_directory': 'high', 'naming_violation': 'low'}}
2025-05-27 00:36:25 - ebook_indexer.core.indexer.EbookIndexer - INFO - Ebook indexer initialized
2025-05-27 00:36:58 - ebook_indexer.core.scanner.DirectoryScanner - INFO - Scanner initialized with extensions: ['.pdf', '.epub', '.mobi', '.azw', '.azw3']
2025-05-27 00:36:58 - ebook_indexer.core.metadata_extractor.MetadataExtractor - INFO - Metadata extractor initialized for formats: ['.pdf', '.epub']
2025-05-27 00:36:58 - ebook_indexer.core.anomaly_detector.AnomalyDetector - INFO - Anomaly detector initialized with config: {'max_nesting_depth': 4, 'enforce_naming_convention': True, 'detect_misplaced_files': True, 'severity_thresholds': {'wrong_level': 'medium', 'deep_nesting': 'low', 'missing_directory': 'high', 'naming_violation': 'low'}}
2025-05-27 00:36:58 - ebook_indexer.core.indexer.EbookIndexer - INFO - Ebook indexer initialized
2025-05-27 00:38:05 - ebook_indexer.core.scanner.DirectoryScanner - INFO - Scanner initialized with extensions: ['.pdf', '.epub', '.mobi', '.azw', '.azw3']
2025-05-27 00:38:05 - ebook_indexer.core.metadata_extractor.MetadataExtractor - INFO - Metadata extractor initialized for formats: ['.pdf', '.epub']
2025-05-27 00:38:05 - ebook_indexer.core.anomaly_detector.AnomalyDetector - INFO - Anomaly detector initialized with config: {'max_nesting_depth': 4, 'enforce_naming_convention': True, 'detect_misplaced_files': True, 'severity_thresholds': {'wrong_level': 'medium', 'deep_nesting': 'low', 'missing_directory': 'high', 'naming_violation': 'low'}}
2025-05-27 00:38:05 - ebook_indexer.core.indexer.EbookIndexer - INFO - Ebook indexer initialized
2025-05-27 00:38:12 - ebook_indexer.core.scanner.DirectoryScanner - INFO - Scanner initialized with extensions: ['.pdf', '.epub', '.mobi', '.azw', '.azw3']
2025-05-27 00:38:12 - ebook_indexer.core.metadata_extractor.MetadataExtractor - INFO - Metadata extractor initialized for formats: ['.pdf', '.epub']
2025-05-27 00:38:12 - ebook_indexer.core.anomaly_detector.AnomalyDetector - INFO - Anomaly detector initialized with config: {'max_nesting_depth': 4, 'enforce_naming_convention': True, 'detect_misplaced_files': True, 'severity_thresholds': {'wrong_level': 'medium', 'deep_nesting': 'low', 'missing_directory': 'high', 'naming_violation': 'low'}}
2025-05-27 00:38:12 - ebook_indexer.core.indexer.EbookIndexer - INFO - Ebook indexer initialized
2025-05-27 00:40:33 - ebook_indexer.core.scanner.DirectoryScanner - INFO - Scanner initialized with extensions: ['.pdf', '.epub', '.mobi', '.azw', '.azw3']
2025-05-27 00:40:33 - ebook_indexer.core.metadata_extractor.MetadataExtractor - INFO - Metadata extractor initialized for formats: ['.pdf', '.epub']
2025-05-27 00:40:33 - ebook_indexer.core.anomaly_detector.AnomalyDetector - INFO - Anomaly detector initialized with config: {'max_nesting_depth': 4, 'enforce_naming_convention': True, 'detect_misplaced_files': True, 'severity_thresholds': {'wrong_level': 'medium', 'deep_nesting': 'low', 'missing_directory': 'high', 'naming_violation': 'low'}}
2025-05-27 00:40:33 - ebook_indexer.core.indexer.EbookIndexer - INFO - Ebook indexer initialized
2025-05-27 00:40:51 - ebook_indexer.core.scanner.DirectoryScanner - INFO - Scanner initialized with extensions: ['.pdf', '.epub', '.mobi', '.azw', '.azw3']
2025-05-27 00:40:51 - ebook_indexer.core.metadata_extractor.MetadataExtractor - INFO - Metadata extractor initialized for formats: ['.pdf', '.epub']
2025-05-27 00:40:51 - ebook_indexer.core.anomaly_detector.AnomalyDetector - INFO - Anomaly detector initialized with config: {'max_nesting_depth': 4, 'enforce_naming_convention': True, 'detect_misplaced_files': True, 'severity_thresholds': {'wrong_level': 'medium', 'deep_nesting': 'low', 'missing_directory': 'high', 'naming_violation': 'low'}}
2025-05-27 00:40:51 - ebook_indexer.core.indexer.EbookIndexer - INFO - Ebook indexer initialized
2025-05-27 00:41:57 - ebook_indexer.database.connection.MongoDBConnection - INFO - Disconnecting from MongoDB
2025-05-27 00:42:05 - ebook_indexer.database.connection.MongoDBConnection - INFO - Connecting to MongoDB at mongodb://localhost:27017
2025-05-27 00:42:05 - ebook_indexer.database.connection.MongoDBConnection - INFO - Successfully connected to database 'ebook_indexer'
2025-05-27 00:42:46 - ebook_indexer.core.scanner.DirectoryScanner - INFO - Scanner initialized with extensions: ['.pdf', '.epub', '.mobi', '.azw', '.azw3']
2025-05-27 00:42:46 - ebook_indexer.core.metadata_extractor.MetadataExtractor - INFO - Metadata extractor initialized for formats: ['.pdf', '.epub']
2025-05-27 00:42:46 - ebook_indexer.core.anomaly_detector.AnomalyDetector - INFO - Anomaly detector initialized with config: {'max_nesting_depth': 4, 'enforce_naming_convention': True, 'detect_misplaced_files': True, 'severity_thresholds': {'wrong_level': 'medium', 'deep_nesting': 'low', 'missing_directory': 'high', 'naming_violation': 'low'}}
2025-05-27 00:42:46 - ebook_indexer.core.indexer.EbookIndexer - INFO - Ebook indexer initialized
2025-05-27 00:47:48 - ebook_indexer.database.connection.MongoDBConnection - INFO - Connecting to MongoDB at mongodb://localhost:27017
2025-05-27 00:47:48 - ebook_indexer.database.connection.MongoDBConnection - INFO - Successfully connected to database 'ebook_indexer'
2025-05-27 00:47:48 - ebook_indexer.database.connection.MongoDBConnection - INFO - Disconnecting from MongoDB
2025-05-27 00:48:27 - ebook_indexer.database.connection.MongoDBConnection - INFO - Connecting to MongoDB at mongodb://localhost:27017
2025-05-27 00:48:27 - ebook_indexer.database.connection.MongoDBConnection - INFO - Successfully connected to database 'ebook_indexer'
2025-05-27 00:48:42 - ebook_indexer.core.scanner.DirectoryScanner - INFO - Scanner initialized with extensions: ['.pdf', '.epub', '.mobi', '.azw', '.azw3']
2025-05-27 00:48:42 - ebook_indexer.core.metadata_extractor.MetadataExtractor - INFO - Metadata extractor initialized for formats: ['.pdf', '.epub']
2025-05-27 00:48:42 - ebook_indexer.core.anomaly_detector.AnomalyDetector - INFO - Anomaly detector initialized with config: {'max_nesting_depth': 4, 'enforce_naming_convention': True, 'detect_misplaced_files': True, 'severity_thresholds': {'wrong_level': 'medium', 'deep_nesting': 'low', 'missing_directory': 'high', 'naming_violation': 'low'}}
2025-05-27 00:48:42 - ebook_indexer.core.indexer.EbookIndexer - INFO - Ebook indexer initialized
2025-05-27 00:48:49 - ebook_indexer.core.scanner.DirectoryScanner - INFO - Scanner initialized with extensions: ['.pdf', '.epub', '.mobi', '.azw', '.azw3']
2025-05-27 00:48:49 - ebook_indexer.core.metadata_extractor.MetadataExtractor - INFO - Metadata extractor initialized for formats: ['.pdf', '.epub']
2025-05-27 00:48:49 - ebook_indexer.core.anomaly_detector.AnomalyDetector - INFO - Anomaly detector initialized with config: {'max_nesting_depth': 4, 'enforce_naming_convention': True, 'detect_misplaced_files': True, 'severity_thresholds': {'wrong_level': 'medium', 'deep_nesting': 'low', 'missing_directory': 'high', 'naming_violation': 'low'}}
2025-05-27 00:48:49 - ebook_indexer.core.indexer.EbookIndexer - INFO - Ebook indexer initialized
2025-05-27 00:48:59 - ebook_indexer.core.scanner.DirectoryScanner - INFO - Scanner initialized with extensions: ['.pdf', '.epub', '.mobi', '.azw', '.azw3']
2025-05-27 00:48:59 - ebook_indexer.core.metadata_extractor.MetadataExtractor - INFO - Metadata extractor initialized for formats: ['.pdf', '.epub']
2025-05-27 00:48:59 - ebook_indexer.core.anomaly_detector.AnomalyDetector - INFO - Anomaly detector initialized with config: {'max_nesting_depth': 4, 'enforce_naming_convention': True, 'detect_misplaced_files': True, 'severity_thresholds': {'wrong_level': 'medium', 'deep_nesting': 'low', 'missing_directory': 'high', 'naming_violation': 'low'}}
2025-05-27 00:48:59 - ebook_indexer.core.indexer.EbookIndexer - INFO - Ebook indexer initialized
2025-05-27 00:49:39 - ebook_indexer.core.scanner.DirectoryScanner - INFO - Scanner initialized with extensions: ['.pdf', '.epub', '.mobi', '.azw', '.azw3']
2025-05-27 00:49:39 - ebook_indexer.core.metadata_extractor.MetadataExtractor - INFO - Metadata extractor initialized for formats: ['.pdf', '.epub']
2025-05-27 00:49:39 - ebook_indexer.core.anomaly_detector.AnomalyDetector - INFO - Anomaly detector initialized with config: {'max_nesting_depth': 4, 'enforce_naming_convention': True, 'detect_misplaced_files': True, 'severity_thresholds': {'wrong_level': 'medium', 'deep_nesting': 'low', 'missing_directory': 'high', 'naming_violation': 'low'}}
2025-05-27 00:49:39 - ebook_indexer.core.indexer.EbookIndexer - INFO - Ebook indexer initialized
2025-05-27 00:51:20 - ebook_indexer.core.scanner.DirectoryScanner - INFO - Scanner initialized with extensions: ['.pdf', '.epub', '.mobi', '.azw', '.azw3']
2025-05-27 00:51:20 - ebook_indexer.core.metadata_extractor.MetadataExtractor - INFO - Metadata extractor initialized for formats: ['.pdf', '.epub']
2025-05-27 00:51:20 - ebook_indexer.core.anomaly_detector.AnomalyDetector - INFO - Anomaly detector initialized with config: {'max_nesting_depth': 4, 'enforce_naming_convention': True, 'detect_misplaced_files': True, 'severity_thresholds': {'wrong_level': 'medium', 'deep_nesting': 'low', 'missing_directory': 'high', 'naming_violation': 'low'}}
2025-05-27 00:51:20 - ebook_indexer.core.indexer.EbookIndexer - INFO - Ebook indexer initialized
2025-05-27 00:51:38 - ebook_indexer.core.scanner.DirectoryScanner - INFO - Scanner initialized with extensions: ['.pdf', '.epub', '.mobi', '.azw', '.azw3']
2025-05-27 00:51:38 - ebook_indexer.core.metadata_extractor.MetadataExtractor - INFO - Metadata extractor initialized for formats: ['.pdf', '.epub']
2025-05-27 00:51:38 - ebook_indexer.core.anomaly_detector.AnomalyDetector - INFO - Anomaly detector initialized with config: {'max_nesting_depth': 4, 'enforce_naming_convention': True, 'detect_misplaced_files': True, 'severity_thresholds': {'wrong_level': 'medium', 'deep_nesting': 'low', 'missing_directory': 'high', 'naming_violation': 'low'}}
2025-05-27 00:51:38 - ebook_indexer.core.indexer.EbookIndexer - INFO - Ebook indexer initialized
2025-05-27 00:52:50 - ebook_indexer.core.scanner.DirectoryScanner - INFO - Scanner initialized with extensions: ['.pdf', '.epub', '.mobi', '.azw', '.azw3']
2025-05-27 00:52:50 - ebook_indexer.core.metadata_extractor.MetadataExtractor - INFO - Metadata extractor initialized for formats: ['.pdf', '.epub']
2025-05-27 00:52:50 - ebook_indexer.core.anomaly_detector.AnomalyDetector - INFO - Anomaly detector initialized with config: {'max_nesting_depth': 4, 'enforce_naming_convention': True, 'detect_misplaced_files': True, 'severity_thresholds': {'wrong_level': 'medium', 'deep_nesting': 'low', 'missing_directory': 'high', 'naming_violation': 'low'}}
2025-05-27 00:52:50 - ebook_indexer.core.indexer.EbookIndexer - INFO - Ebook indexer initialized
