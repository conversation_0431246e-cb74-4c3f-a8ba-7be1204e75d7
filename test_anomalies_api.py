#!/usr/bin/env python3
"""
Test script for the anomalies API endpoints.
"""

import asyncio
import sys
import os
from pathlib import Path

# Add the project root to the Python path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from api.routers.anomalies import router
from fastapi.testclient import TestClient
from fastapi import FastAPI

# Create a test app
app = FastAPI()
app.include_router(router, prefix="/api/v1/anomalies")

client = TestClient(app)

def test_anomalies_endpoints():
    """Test the anomalies API endpoints."""
    print("Testing anomalies API endpoints...")
    
    # Test 1: List anomalies endpoint
    print("\n1. Testing GET /api/v1/anomalies")
    try:
        response = client.get("/api/v1/anomalies")
        print(f"Status: {response.status_code}")
        if response.status_code == 200:
            data = response.json()
            print(f"Response keys: {list(data.keys())}")
            if 'anomalies' in data:
                print(f"Number of anomalies: {len(data['anomalies'])}")
        else:
            print(f"Error: {response.text}")
    except Exception as e:
        print(f"Error testing list anomalies: {e}")
    
    # Test 2: Open file location endpoint (with a test file)
    print("\n2. Testing POST /api/v1/anomalies/open-file-location")
    test_file_path = __file__  # Use this script as a test file
    try:
        response = client.post(
            "/api/v1/anomalies/open-file-location",
            json={"file_path": test_file_path}
        )
        print(f"Status: {response.status_code}")
        if response.status_code == 200:
            data = response.json()
            print(f"Response: {data}")
        else:
            print(f"Error: {response.text}")
    except Exception as e:
        print(f"Error testing open file location: {e}")
    
    # Test 3: Open file location with non-existent file
    print("\n3. Testing POST /api/v1/anomalies/open-file-location with non-existent file")
    try:
        response = client.post(
            "/api/v1/anomalies/open-file-location",
            json={"file_path": "/non/existent/file.txt"}
        )
        print(f"Status: {response.status_code}")
        print(f"Response: {response.text}")
    except Exception as e:
        print(f"Error testing non-existent file: {e}")

def test_api_types():
    """Test that our API types are correctly defined."""
    print("\n\nTesting API type definitions...")
    
    try:
        from api.models.responses import AnomalyResponse, AnomalyListResponse
        print("✓ AnomalyResponse and AnomalyListResponse imported successfully")
        
        # Test creating an AnomalyResponse instance
        from datetime import datetime
        anomaly = AnomalyResponse(
            id="test-id",
            book_id="test-book-id",
            file_path="/test/path.pdf",
            anomaly_type="misplaced_file",
            severity="high",
            description="Test anomaly",
            resolved=False,
            detected_date=datetime.now(),
            collection_name="test-collection"
        )
        print("✓ AnomalyResponse instance created successfully")
        print(f"  - ID: {anomaly.id}")
        print(f"  - File Path: {anomaly.file_path}")
        print(f"  - Type: {anomaly.anomaly_type}")
        print(f"  - Severity: {anomaly.severity}")
        
    except Exception as e:
        print(f"✗ Error with API types: {e}")

if __name__ == "__main__":
    print("=" * 60)
    print("ANOMALIES API TEST SUITE")
    print("=" * 60)
    
    test_api_types()
    
    # Note: The endpoint tests require the full application context
    # including database connections, so they may fail in isolation
    print("\n" + "=" * 60)
    print("ENDPOINT TESTS (may require full app context)")
    print("=" * 60)
    
    test_anomalies_endpoints()
    
    print("\n" + "=" * 60)
    print("TEST COMPLETE")
    print("=" * 60)
