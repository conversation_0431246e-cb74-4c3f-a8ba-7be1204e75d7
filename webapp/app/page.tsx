'use client';

import { useAppState } from '@/hooks/useAppState';
import { SystemHealthCard } from '@/components/dashboard/SystemHealthCard';
import { SummaryCards } from '@/components/dashboard/SummaryCards';
import { OperationsPanel } from '@/components/dashboard/OperationsPanel';
import { RecentJobsCard } from '@/components/dashboard/RecentJobsCard';
import { AnomaliesTable } from '@/components/dashboard/AnomaliesTable';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Button } from '@/components/ui/button';
import { Loader2, RefreshCw, AlertCircle } from 'lucide-react';

export default function Dashboard() {
  const { data: appState, loading, error, refetch } = useAppState();

  if (loading && !appState) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <div className="text-center">
          <Loader2 className="h-8 w-8 animate-spin mx-auto mb-4" />
          <p className="text-muted-foreground">Loading dashboard...</p>
        </div>
      </div>
    );
  }

  if (error && !appState) {
    return (
      <div className="max-w-2xl mx-auto">
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription className="flex items-center justify-between">
            <span>Failed to load dashboard: {error}</span>
            <Button variant="outline" size="sm" onClick={refetch}>
              <RefreshCw className="h-4 w-4 mr-2" />
              Retry
            </Button>
          </AlertDescription>
        </Alert>
      </div>
    );
  }

  if (!appState) {
    return (
      <div className="text-center py-8">
        <p className="text-muted-foreground">No data available</p>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Error banner if there's an error but we have cached data */}
      {error && (
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription className="flex items-center justify-between">
            <span>Connection issue: {error} (showing cached data)</span>
            <Button variant="outline" size="sm" onClick={refetch}>
              <RefreshCw className="h-4 w-4 mr-2" />
              Retry
            </Button>
          </AlertDescription>
        </Alert>
      )}

      {/* Summary Cards */}
      <SummaryCards summary={appState.dashboard_summary} />

      {/* Main Content Grid */}
      <div className="grid gap-6 lg:grid-cols-3">
        {/* Left Column - System Health */}
        <div className="lg:col-span-1">
          <SystemHealthCard systemHealth={appState.system_health} />
        </div>

        {/* Right Column - Operations and Jobs */}
        <div className="lg:col-span-2 space-y-6">
          <OperationsPanel activeOperations={appState.active_operations} />
          <RecentJobsCard jobs={appState.recent_jobs} />
        </div>
      </div>

      {/* Auto-refresh indicator */}
      <div className="text-center text-xs text-muted-foreground">
        <div className="flex items-center justify-center gap-2">
          {loading ? (
            <>
              <Loader2 className="h-3 w-3 animate-spin" />
              Refreshing...
            </>
          ) : (
            <>
              <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse" />
              Auto-refreshing every 5 seconds
            </>
          )}
        </div>
      </div>
    </div>
  );
}
