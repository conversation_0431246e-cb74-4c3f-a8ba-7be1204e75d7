// API client for the Ebook Indexer backend

const API_BASE_URL = process.env.NODE_ENV === 'production'
  ? 'http://localhost:8000'
  : 'http://localhost:8000';

// Types based on the API response models
export interface DashboardSummary {
  total_books: number;
  total_collections: number;
  total_anomalies: number;
  active_jobs: number;
  last_scan_date?: string;
}

export interface SystemHealth {
  status: string;
  database: any;
  configuration: any;
  version: string;
  timestamp: string;
}

export interface JobResponse {
  id: string;
  status: string;
  root_directories: string[];
  statistics: {
    total_files: number;
    processed_files: number;
    successful_files: number;
    failed_files: number;
    anomalous_files: number;
    collections_found: number;
    processing_rate_files_per_sec: number;
  };
  timing: {
    start_time?: string;
    end_time?: string;
    duration_seconds?: number;
    estimated_completion?: string;
  };
  created_date: string;
}

export interface OperationStatus {
  operation_id: string;
  type: string;
  status: string;
  progress: {
    processed: number;
    total: number;
    percentage: number;
    eta_seconds?: number;
    current_item?: string;
  };
  results: {
    successful: number;
    failed: number;
    still_anomalous: number;
  };
  started_at: string;
  estimated_completion?: string;
}

export interface AppState {
  system_health: SystemHealth;
  dashboard_summary: DashboardSummary;
  active_operations: OperationStatus[];
  recent_jobs: JobResponse[];
}

export interface OperationStartResponse {
  operation_id: string;
  status: string;
  estimated_files: number;
  message: string;
}

export interface AnomalyResponse {
  id: string;
  book_id: string;
  file_path: string;
  anomaly_type: string;
  severity: string;
  description: string;
  suggested_action?: string;
  resolved: boolean;
  detected_date: string;
  collection_name?: string;
}

export interface AnomalySummaryStats {
  total_anomalies: number;
  by_severity: Record<string, number>;
  by_type: Record<string, number>;
  resolved: number;
  unresolved: number;
}

export interface PaginationInfo {
  page: number;
  per_page: number;
  total_pages: number;
  total_items: number;
  has_next: boolean;
  has_prev: boolean;
}

export interface AnomalyListResponse {
  anomalies: AnomalyResponse[];
  summary: AnomalySummaryStats;
  pagination: PaginationInfo;
}

// API functions
export async function fetchAppState(): Promise<AppState> {
  const response = await fetch(`${API_BASE_URL}/api/v1/dashboard/app-state`);
  if (!response.ok) {
    throw new Error(`Failed to fetch app state: ${response.statusText}`);
  }
  return response.json();
}

export async function fetchDashboardOverview() {
  const response = await fetch(`${API_BASE_URL}/api/v1/dashboard/overview`);
  if (!response.ok) {
    throw new Error(`Failed to fetch dashboard overview: ${response.statusText}`);
  }
  return response.json();
}

export async function fetchSystemStatus() {
  const response = await fetch(`${API_BASE_URL}/api/v1/dashboard/system-status`);
  if (!response.ok) {
    throw new Error(`Failed to fetch system status: ${response.statusText}`);
  }
  return response.json();
}

export async function triggerRetryOperation(jobId?: string): Promise<OperationStartResponse> {
  const response = await fetch(`${API_BASE_URL}/api/v1/operations/retry`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify({ job_id: jobId }),
  });

  if (!response.ok) {
    throw new Error(`Failed to trigger retry operation: ${response.statusText}`);
  }

  return response.json();
}

export async function triggerIndexOperation(
  rootDirectories: string[],
  jobId?: string,
  resume?: boolean,
  forceReindex?: boolean
): Promise<OperationStartResponse> {
  const response = await fetch(`${API_BASE_URL}/api/v1/operations/index`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify({
      root_directories: rootDirectories,
      job_id: jobId,
      resume: resume,
      force_reindex: forceReindex
    }),
  });

  if (!response.ok) {
    throw new Error(`Failed to trigger index operation: ${response.statusText}`);
  }

  return response.json();
}

export async function fetchOperationStatus(operationId: string): Promise<OperationStatus> {
  const response = await fetch(`${API_BASE_URL}/api/v1/operations/${operationId}`);
  if (!response.ok) {
    throw new Error(`Failed to fetch operation status: ${response.statusText}`);
  }
  return response.json();
}

export async function fetchOperations(): Promise<OperationStatus[]> {
  const response = await fetch(`${API_BASE_URL}/api/v1/operations`);
  if (!response.ok) {
    throw new Error(`Failed to fetch operations: ${response.statusText}`);
  }
  return response.json();
}

export async function cancelOperation(operationId: string) {
  const response = await fetch(`${API_BASE_URL}/api/v1/operations/${operationId}`, {
    method: 'DELETE',
  });

  if (!response.ok) {
    throw new Error(`Failed to cancel operation: ${response.statusText}`);
  }

  return response.json();
}

export async function fetchJobs(page = 1, perPage = 10, status?: string) {
  const params = new URLSearchParams({
    page: page.toString(),
    per_page: perPage.toString(),
  });

  if (status) {
    params.append('status', status);
  }

  const response = await fetch(`${API_BASE_URL}/api/v1/jobs?${params}`);
  if (!response.ok) {
    throw new Error(`Failed to fetch jobs: ${response.statusText}`);
  }
  return response.json();
}

export async function fetchJobStatus(jobId: string): Promise<JobResponse> {
  const response = await fetch(`${API_BASE_URL}/api/v1/jobs/${jobId}/status`);
  if (!response.ok) {
    throw new Error(`Failed to fetch job status: ${response.statusText}`);
  }
  return response.json();
}

export async function fetchAnomalies(
  page = 1,
  perPage = 10,
  severity?: string,
  type?: string,
  resolved?: boolean
): Promise<AnomalyListResponse> {
  const params = new URLSearchParams({
    page: page.toString(),
    per_page: perPage.toString(),
  });

  if (severity) params.append('severity', severity);
  if (type) params.append('type', type);
  if (resolved !== undefined) params.append('resolved', resolved.toString());

  const response = await fetch(`${API_BASE_URL}/api/v1/anomalies?${params}`);
  if (!response.ok) {
    throw new Error(`Failed to fetch anomalies: ${response.statusText}`);
  }
  return response.json();
}

export async function openFileLocation(filePath: string): Promise<{ success: boolean; message: string }> {
  const response = await fetch(`${API_BASE_URL}/api/v1/anomalies/open-file-location`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify({ file_path: filePath }),
  });

  if (!response.ok) {
    throw new Error(`Failed to open file location: ${response.statusText}`);
  }

  return response.json();
}

export async function reindexFile(anomalyId: string, force: boolean = true): Promise<OperationStartResponse> {
  const response = await fetch(`${API_BASE_URL}/api/v1/operations/reindex-file`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify({
      anomaly_id: anomalyId,
      force: force
    }),
  });

  if (!response.ok) {
    throw new Error(`Failed to reindex file: ${response.statusText}`);
  }

  return response.json();
}
