<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Clipboard Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 50px auto;
            padding: 20px;
        }
        .test-item {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        button {
            padding: 8px 16px;
            margin: 5px;
            border: 1px solid #ccc;
            border-radius: 4px;
            background: #f8f9fa;
            cursor: pointer;
        }
        button:hover {
            background: #e9ecef;
        }
        .success {
            color: green;
            font-weight: bold;
        }
        .error {
            color: red;
            font-weight: bold;
        }
        .path {
            font-family: monospace;
            background: #f1f3f4;
            padding: 4px 8px;
            border-radius: 3px;
            margin: 5px 0;
        }
    </style>
</head>
<body>
    <h1>Clipboard Functionality Test</h1>
    <p>This page tests the clipboard functionality that will be used in the anomalies table.</p>
    
    <div class="test-item">
        <h3>Test 1: Copy File Path</h3>
        <div class="path">/home/<USER>/Documents/ebooks/collection1/book-title/book.pdf</div>
        <button onclick="copyToClipboard('/home/<USER>/Documents/ebooks/collection1/book-title/book.pdf', 'result1')">
            📋 Copy Path
        </button>
        <div id="result1"></div>
    </div>
    
    <div class="test-item">
        <h3>Test 2: Copy Long Path</h3>
        <div class="path">/very/long/path/to/some/deeply/nested/directory/structure/with/many/levels/ebooks/collection-name-2024/author-name/book-series/book-title-volume-1/book.epub</div>
        <button onclick="copyToClipboard('/very/long/path/to/some/deeply/nested/directory/structure/with/many/levels/ebooks/collection-name-2024/author-name/book-series/book-title-volume-1/book.epub', 'result2')">
            📋 Copy Long Path
        </button>
        <div id="result2"></div>
    </div>
    
    <div class="test-item">
        <h3>Test 3: Copy Path with Special Characters</h3>
        <div class="path">/home/<USER>/Documents/ebooks/Sci-Fi & Fantasy/Author's Name/Book Title (2024)/book file.pdf</div>
        <button onclick="copyToClipboard('/home/<USER>/Documents/ebooks/Sci-Fi & Fantasy/Author\\'s Name/Book Title (2024)/book file.pdf', 'result3')">
            📋 Copy Special Path
        </button>
        <div id="result3"></div>
    </div>
    
    <div class="test-item">
        <h3>Instructions</h3>
        <ol>
            <li>Click any "Copy Path" button above</li>
            <li>The button should show a success message</li>
            <li>Open a text editor and paste (Ctrl+V or Cmd+V)</li>
            <li>Verify the full file path was copied correctly</li>
        </ol>
    </div>

    <script>
        async function copyToClipboard(text, resultId) {
            const resultDiv = document.getElementById(resultId);
            const button = event.target;
            const originalText = button.textContent;
            
            try {
                await navigator.clipboard.writeText(text);
                resultDiv.innerHTML = '<span class="success">✅ Copied to clipboard!</span>';
                button.textContent = '✅ Copied!';
                button.style.background = '#d4edda';
                
                // Reset after 2 seconds
                setTimeout(() => {
                    resultDiv.innerHTML = '';
                    button.textContent = originalText;
                    button.style.background = '#f8f9fa';
                }, 2000);
                
            } catch (err) {
                console.error('Failed to copy: ', err);
                resultDiv.innerHTML = '<span class="error">❌ Failed to copy. Error: ' + err.message + '</span>';
                
                // Try fallback method
                try {
                    const textArea = document.createElement('textarea');
                    textArea.value = text;
                    document.body.appendChild(textArea);
                    textArea.select();
                    document.execCommand('copy');
                    document.body.removeChild(textArea);
                    
                    resultDiv.innerHTML = '<span class="success">✅ Copied using fallback method!</span>';
                    button.textContent = '✅ Copied!';
                    button.style.background = '#d4edda';
                    
                    setTimeout(() => {
                        resultDiv.innerHTML = '';
                        button.textContent = originalText;
                        button.style.background = '#f8f9fa';
                    }, 2000);
                    
                } catch (fallbackErr) {
                    resultDiv.innerHTML = '<span class="error">❌ Both clipboard methods failed</span>';
                }
            }
        }
        
        // Test if clipboard API is available
        if (!navigator.clipboard) {
            document.body.insertAdjacentHTML('afterbegin', 
                '<div style="background: #fff3cd; border: 1px solid #ffeaa7; padding: 10px; margin-bottom: 20px; border-radius: 5px;">' +
                '<strong>⚠️ Note:</strong> Clipboard API not available. This might be because the page is not served over HTTPS. ' +
                'The fallback method will be used instead.' +
                '</div>'
            );
        }
    </script>
</body>
</html>
