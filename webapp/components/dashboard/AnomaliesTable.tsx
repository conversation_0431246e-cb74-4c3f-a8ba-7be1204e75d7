import { useState, useEffect } from "react";
import { <PERSON>, <PERSON><PERSON>onte<PERSON>, Card<PERSON><PERSON>er, Card<PERSON>itle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { 
  AnomalyResponse, 
  AnomalyListResponse, 
  fetchAnomalies, 
  openFileLocation 
} from "@/lib/api";
import { 
  AlertTriangle, 
  ExternalLink, 
  FolderOpen, 
  ChevronLeft, 
  ChevronRight,
  Filter,
  Loader2
} from "lucide-react";

interface AnomaliesTableProps {
  className?: string;
}

export function AnomaliesTable({ className }: AnomaliesTableProps) {
  const [anomalies, setAnomalies] = useState<AnomalyResponse[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [filters, setFilters] = useState({
    severity: '',
    type: '',
    resolved: undefined as boolean | undefined
  });

  const loadAnomalies = async () => {
    try {
      setLoading(true);
      setError(null);
      
      const response: AnomalyListResponse = await fetchAnomalies(
        currentPage,
        10,
        filters.severity || undefined,
        filters.type || undefined,
        filters.resolved
      );
      
      setAnomalies(response.anomalies);
      setTotalPages(response.pagination.total_pages);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to load anomalies');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    loadAnomalies();
  }, [currentPage, filters]);

  const handleOpenFileLocation = async (filePath: string) => {
    try {
      await openFileLocation(filePath);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to open file location');
    }
  };

  const getSeverityColor = (severity: string) => {
    switch (severity.toLowerCase()) {
      case 'high':
        return 'bg-red-100 text-red-800 border-red-200';
      case 'medium':
        return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      case 'low':
        return 'bg-blue-100 text-blue-800 border-blue-200';
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  const getTypeColor = (type: string) => {
    switch (type) {
      case 'misplaced_file':
        return 'bg-orange-100 text-orange-800 border-orange-200';
      case 'deep_nesting':
        return 'bg-purple-100 text-purple-800 border-purple-200';
      case 'naming_convention':
        return 'bg-indigo-100 text-indigo-800 border-indigo-200';
      case 'duplicate_file':
        return 'bg-pink-100 text-pink-800 border-pink-200';
      case 'missing_directory':
        return 'bg-green-100 text-green-800 border-green-200';
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  const formatType = (type: string) => {
    return type.split('_').map(word => 
      word.charAt(0).toUpperCase() + word.slice(1)
    ).join(' ');
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const truncatePath = (path: string, maxLength: number = 50) => {
    if (path.length <= maxLength) return path;
    const start = path.substring(0, 15);
    const end = path.substring(path.length - 30);
    return `${start}...${end}`;
  };

  return (
    <Card className={className}>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <AlertTriangle className="h-5 w-5" />
          Anomalies
        </CardTitle>
      </CardHeader>
      <CardContent>
        {error && (
          <Alert className="mb-4" variant="destructive">
            <AlertTriangle className="h-4 w-4" />
            <AlertDescription>{error}</AlertDescription>
          </Alert>
        )}

        {loading ? (
          <div className="flex items-center justify-center py-8">
            <Loader2 className="h-6 w-6 animate-spin" />
          </div>
        ) : anomalies.length === 0 ? (
          <div className="text-center py-8 text-muted-foreground">
            No anomalies found
          </div>
        ) : (
          <>
            <div className="overflow-x-auto">
              <table className="w-full border-collapse">
                <thead>
                  <tr className="border-b">
                    <th className="text-left p-2 font-medium">File Path</th>
                    <th className="text-left p-2 font-medium">Type</th>
                    <th className="text-left p-2 font-medium">Severity</th>
                    <th className="text-left p-2 font-medium">Description</th>
                    <th className="text-left p-2 font-medium">Status</th>
                    <th className="text-left p-2 font-medium">Detected</th>
                    <th className="text-left p-2 font-medium">Actions</th>
                  </tr>
                </thead>
                <tbody>
                  {anomalies.map((anomaly) => (
                    <tr key={anomaly.id} className="border-b hover:bg-gray-50">
                      <td className="p-2">
                        <div className="flex items-center gap-2">
                          <span 
                            className="font-mono text-sm cursor-pointer hover:text-blue-600 hover:underline"
                            title={anomaly.file_path}
                            onClick={() => handleOpenFileLocation(anomaly.file_path)}
                          >
                            {truncatePath(anomaly.file_path)}
                          </span>
                          <Button
                            size="sm"
                            variant="ghost"
                            className="h-6 w-6 p-0"
                            onClick={() => handleOpenFileLocation(anomaly.file_path)}
                            title="Open file location"
                          >
                            <FolderOpen className="h-3 w-3" />
                          </Button>
                        </div>
                      </td>
                      <td className="p-2">
                        <Badge className={getTypeColor(anomaly.anomaly_type)}>
                          {formatType(anomaly.anomaly_type)}
                        </Badge>
                      </td>
                      <td className="p-2">
                        <Badge className={getSeverityColor(anomaly.severity)}>
                          {anomaly.severity.toUpperCase()}
                        </Badge>
                      </td>
                      <td className="p-2 max-w-xs">
                        <span className="text-sm" title={anomaly.description}>
                          {anomaly.description.length > 60 
                            ? `${anomaly.description.substring(0, 60)}...` 
                            : anomaly.description}
                        </span>
                      </td>
                      <td className="p-2">
                        <Badge variant={anomaly.resolved ? "default" : "secondary"}>
                          {anomaly.resolved ? "Resolved" : "Open"}
                        </Badge>
                      </td>
                      <td className="p-2 text-sm text-muted-foreground">
                        {formatDate(anomaly.detected_date)}
                      </td>
                      <td className="p-2">
                        <div className="flex gap-1">
                          <Button
                            size="sm"
                            variant="outline"
                            className="h-7 px-2"
                            onClick={() => handleOpenFileLocation(anomaly.file_path)}
                          >
                            <ExternalLink className="h-3 w-3" />
                          </Button>
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>

            {/* Pagination */}
            {totalPages > 1 && (
              <div className="flex items-center justify-between mt-4">
                <div className="text-sm text-muted-foreground">
                  Page {currentPage} of {totalPages}
                </div>
                <div className="flex gap-2">
                  <Button
                    size="sm"
                    variant="outline"
                    onClick={() => setCurrentPage(prev => Math.max(1, prev - 1))}
                    disabled={currentPage === 1}
                  >
                    <ChevronLeft className="h-4 w-4" />
                  </Button>
                  <Button
                    size="sm"
                    variant="outline"
                    onClick={() => setCurrentPage(prev => Math.min(totalPages, prev + 1))}
                    disabled={currentPage === totalPages}
                  >
                    <ChevronRight className="h-4 w-4" />
                  </Button>
                </div>
              </div>
            )}
          </>
        )}
      </CardContent>
    </Card>
  );
}
