import { useState, useEffect, useCallback } from 'react';
import { AnomalyResponse, AnomalyListResponse, fetchAnomalies } from '@/lib/api';

interface UseAnomaliesOptions {
  page?: number;
  perPage?: number;
  severity?: string;
  type?: string;
  resolved?: boolean;
  autoRefresh?: boolean;
  refreshInterval?: number;
}

interface UseAnomaliesReturn {
  anomalies: AnomalyResponse[];
  loading: boolean;
  error: string | null;
  pagination: {
    currentPage: number;
    totalPages: number;
    totalItems: number;
    hasNext: boolean;
    hasPrev: boolean;
  };
  summary: {
    total_anomalies: number;
    by_severity: Record<string, number>;
    by_type: Record<string, number>;
    resolved: number;
    unresolved: number;
  } | null;
  refetch: () => Promise<void>;
  setPage: (page: number) => void;
  setFilters: (filters: {
    severity?: string;
    type?: string;
    resolved?: boolean;
  }) => void;
}

export function useAnomalies(options: UseAnomaliesOptions = {}): UseAnomaliesReturn {
  const {
    page = 1,
    perPage = 10,
    severity,
    type,
    resolved,
    autoRefresh = false,
    refreshInterval = 30000
  } = options;

  const [anomalies, setAnomalies] = useState<AnomalyResponse[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [currentPage, setCurrentPage] = useState(page);
  const [pagination, setPagination] = useState({
    currentPage: 1,
    totalPages: 1,
    totalItems: 0,
    hasNext: false,
    hasPrev: false
  });
  const [summary, setSummary] = useState<UseAnomaliesReturn['summary']>(null);
  const [filters, setFiltersState] = useState({
    severity,
    type,
    resolved
  });

  const fetchData = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);

      const response: AnomalyListResponse = await fetchAnomalies(
        currentPage,
        perPage,
        filters.severity,
        filters.type,
        filters.resolved
      );

      setAnomalies(response.anomalies);
      setSummary(response.summary);
      setPagination({
        currentPage: response.pagination.page,
        totalPages: response.pagination.total_pages,
        totalItems: response.pagination.total_items,
        hasNext: response.pagination.has_next,
        hasPrev: response.pagination.has_prev
      });
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to fetch anomalies');
      setAnomalies([]);
      setSummary(null);
    } finally {
      setLoading(false);
    }
  }, [currentPage, perPage, filters.severity, filters.type, filters.resolved]);

  useEffect(() => {
    fetchData();
  }, [fetchData]);

  useEffect(() => {
    if (!autoRefresh) return;

    const interval = setInterval(fetchData, refreshInterval);
    return () => clearInterval(interval);
  }, [autoRefresh, refreshInterval, fetchData]);

  const setPage = useCallback((page: number) => {
    setCurrentPage(page);
  }, []);

  const setFilters = useCallback((newFilters: {
    severity?: string;
    type?: string;
    resolved?: boolean;
  }) => {
    setFiltersState(prev => ({
      ...prev,
      ...newFilters
    }));
    setCurrentPage(1); // Reset to first page when filters change
  }, []);

  const refetch = useCallback(async () => {
    await fetchData();
  }, [fetchData]);

  return {
    anomalies,
    loading,
    error,
    pagination,
    summary,
    refetch,
    setPage,
    setFilters
  };
}
