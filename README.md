# Ebook Indexer

A comprehensive ebook directory analysis and indexing tool with anomaly detection.

## 🚀 Phase 3 Complete - Full Application Ready!

**Status**: ✅ **COMPLETED** - All core components implemented and tested

### What's New in Phase 3

✅ **Anomaly Detector** - Detects structural and organizational issues
✅ **Main Indexer** - Orchestrates the complete indexing workflow
✅ **Progress Tracker** - Real-time progress monitoring with ETA
✅ **CLI Interface** - Full command-line interface with rich output
✅ **Integration Tests** - Comprehensive testing of all components
✅ **Demo Application** - Complete working demonstration

### Previous Phases Completed

✅ **Phase 1: Foundation & Core Infrastructure**
- Project setup with uv and pyproject.toml
- Configuration management system with YAML support
- Custom exception classes and logging configuration
- Comprehensive test suite (65+ tests passing)

✅ **Phase 2: Data Models & Database Foundation**
- Pydantic models for MongoDB documents
- MongoDB connection management with health checks
- Repository pattern with CRUD operations
- Database indexes for performance optimization

## Features

- **Directory Scanning**: Recursively scan directories for ebook files
- **Metadata Extraction**: Extract metadata from PDF and EPUB files
- **Anomaly Detection**: Detect organizational issues and naming problems
- **Database Storage**: Store results in MongoDB with full CRUD operations
- **Progress Tracking**: Real-time progress monitoring for long operations
- **CLI Interface**: Command-line interface for all operations
- **Configurable**: YAML-based configuration with environment variable support

## Supported Formats

- PDF (.pdf) ✅
- EPUB (.epub) ✅
- Plain text (.txt) ✅
- MOBI (.mobi) - planned
- AZW/AZW3 (.azw, .azw3) - planned
- FB2 (.fb2) - planned

## Installation

```bash
# Clone the repository
git clone <repository-url>
cd ebook-indexer

# Install with uv (recommended)
uv sync

# Or install with pip
pip install -e .
```

## Quick Start

### 1. Try the Demo (No Database Required)
```bash
# Run the complete demo to see all features
uv run python demo_complete_app.py
```

### 2. Use the CLI Interface
```bash
# Show help
ebook-indexer --help

# Index a directory (without database - will show errors but demonstrate functionality)
ebook-indexer index --roots /path/to/your/ebooks

# Show available commands
ebook-indexer stats --help
ebook-indexer anomalies --help
ebook-indexer export --help
```

### 3. Full Setup with Database
```bash
# 1. Start MongoDB
docker run -d -p 27017:27017 --name mongodb mongo:latest

# 2. Configure the application
cp config/config.example.yaml config/config.yaml
# Edit config.yaml with your MongoDB settings

# 3. Run indexing
ebook-indexer index --roots /path/to/your/ebooks

# 4. View results
ebook-indexer stats
ebook-indexer anomalies
ebook-indexer export --format json
```

## Anomaly Detection

The system detects various organizational issues:

- **Misplaced Files**: Files not following expected directory structure
- **Deep Nesting**: Directories nested too deeply (configurable threshold)
- **Naming Violations**: Problematic characters in filenames/directories
- **Missing Directories**: Files that should be in book-specific subdirectories

Example anomaly output:
```
📁 misplaced-book.pdf
   ⚠️  misplaced_file (medium)
      File at level 1, expected 3. File may be misplaced in collection root.
      💡 Move file to appropriate book directory
```

## CLI Commands

- `index`: Run ebook indexing with anomaly detection
- `stats`: Show processing statistics
- `anomalies`: Show detected anomalies with filtering options
- `retry`: Retry failed files and recheck anomalies
- `export`: Export results to JSON/CSV formats

### Command Examples

```bash
# Index specific directories
ebook-indexer index --roots /books/fiction /books/technical

# Force reindexing of already indexed directories
ebook-indexer index --roots /books/fiction --force

# Resume interrupted job
ebook-indexer index --resume --job-id abc123

# Show anomalies with filtering
ebook-indexer anomalies --severity high --type misplaced_file

# Export to CSV
ebook-indexer export --format csv --output results.csv --include-anomalies
```

## Development

```bash
# Install development dependencies
uv sync --dev

# Run tests
uv run pytest

# Run specific test categories
uv run pytest ebook_indexer/tests/test_integration.py -v

# Run demo
uv run python demo_complete_app.py

# Run CLI directly
uv run python -m ebook_indexer.main --help
uv run ebook-indexer --help
```

## Services

```bash
docker compose up
```


## API

```bash
uv run uvicorn api.main:app --host 0.0.0.0 --port 8001
```


## Web

```bash
npm run dev
```

http://localhost:3000/



## Architecture

The application follows a clean, modular architecture:

```
ebook_indexer/
├── core/           # Business logic
│   ├── scanner.py          # Directory scanning ✅
│   ├── indexer.py          # Main orchestrator ✅
│   ├── metadata_extractor.py  # Metadata extraction ✅
│   └── anomaly_detector.py    # Anomaly detection ✅
├── database/       # Data layer
│   ├── models.py           # Pydantic models ✅
│   ├── repository.py       # Repository pattern ✅
│   └── connection.py       # MongoDB connection ✅
├── utils/          # Utilities
│   ├── file_utils.py       # File operations ✅
│   ├── progress_tracker.py # Progress monitoring ✅
│   └── logging_config.py   # Logging setup ✅
├── config/         # Configuration ✅
├── exceptions/     # Custom exceptions ✅
└── main.py         # CLI interface ✅
```

## Current Status

### ✅ Completed Components

1. **Foundation Layer** (Phase 1)
   - Configuration management
   - Logging system
   - Exception handling
   - File utilities

2. **Data Layer** (Phase 2)
   - MongoDB models and schemas
   - Repository pattern with CRUD operations
   - Database connection management
   - Comprehensive test suite (65+ tests)

3. **Business Logic** (Phase 3) - **NEW!**
   - Directory scanner with structure analysis
   - Metadata extractor for multiple formats
   - Anomaly detector with configurable rules
   - Main indexer orchestrator
   - Progress tracking with real-time updates
   - Full CLI interface with rich output

### 🎯 Next Steps (Optional Enhancements)

- **Enhanced Metadata**: Support for more ebook formats (MOBI, AZW)
- **Web Interface**: Optional web UI for browsing results
- **Advanced Analytics**: Collection analysis and recommendations
- **Export Formats**: Additional export options (XML, database dumps)
- **Performance**: Parallel processing and caching optimizations

## License

MIT License
