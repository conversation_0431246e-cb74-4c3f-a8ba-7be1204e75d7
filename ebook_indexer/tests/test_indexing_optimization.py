"""Tests for indexing optimization features."""

import pytest
from unittest.mock import Mock, patch, MagicMock
from datetime import datetime
from pathlib import Path

from ebook_indexer.core.indexer import EbookIndexer
from ebook_indexer.database.repository import JobRepository
from ebook_indexer.database.models import <PERSON>ing<PERSON><PERSON>, JobStatus
from ebook_indexer.config.settings import AppConfig
from ebook_indexer.exceptions.custom_exceptions import IndexingError


class TestIndexingOptimization:
    """Test indexing optimization features."""

    def setup_method(self):
        """Set up test fixtures."""
        self.config = AppConfig()
        self.config.root_directories = ["/test/books1", "/test/books2"]

        # Mock repositories
        self.mock_job_repo = Mock(spec=JobRepository)
        self.mock_book_repo = Mock()
        self.mock_anomaly_repo = Mock()

        # Create indexer with mocked dependencies
        with patch('ebook_indexer.core.indexer.JobRepository', return_value=self.mock_job_repo), \
             patch('ebook_indexer.core.indexer.BookRepository', return_value=self.mock_book_repo), \
             patch('ebook_indexer.core.indexer.AnomalyRepository', return_value=self.mock_anomaly_repo):
            self.indexer = EbookIndexer(self.config)

        # Replace the repository instances with our mocks
        self.indexer.job_repository = self.mock_job_repo
        self.indexer.book_repository = self.mock_book_repo
        self.indexer.anomaly_repository = self.mock_anomaly_repo

    def test_get_directories_needing_indexing_all_new(self):
        """Test filtering when all directories are new."""
        directories = ["/test/books1", "/test/books2", "/test/books3"]

        # Mock no completed jobs
        self.mock_job_repo.get_directories_needing_indexing.return_value = directories

        result = self.mock_job_repo.get_directories_needing_indexing(directories)

        assert result == directories
        self.mock_job_repo.get_directories_needing_indexing.assert_called_once_with(directories)

    def test_get_directories_needing_indexing_some_indexed(self):
        """Test filtering when some directories are already indexed."""
        directories = ["/test/books1", "/test/books2", "/test/books3"]

        # Mock that books1 and books2 are already indexed
        self.mock_job_repo.get_directories_needing_indexing.return_value = ["/test/books3"]

        result = self.mock_job_repo.get_directories_needing_indexing(directories)

        assert result == ["/test/books3"]
        self.mock_job_repo.get_directories_needing_indexing.assert_called_once_with(directories)

    def test_get_directories_needing_indexing_all_indexed(self):
        """Test filtering when all directories are already indexed."""
        directories = ["/test/books1", "/test/books2"]

        # Mock that all directories are already indexed
        self.mock_job_repo.get_directories_needing_indexing.return_value = []

        result = self.mock_job_repo.get_directories_needing_indexing(directories)

        assert result == []
        self.mock_job_repo.get_directories_needing_indexing.assert_called_once_with(directories)

    def test_is_directory_successfully_indexed_true(self):
        """Test checking if directory is indexed - returns True."""
        directory = "/test/books1"

        self.mock_job_repo.is_directory_successfully_indexed.return_value = True

        result = self.mock_job_repo.is_directory_successfully_indexed(directory)

        assert result is True
        self.mock_job_repo.is_directory_successfully_indexed.assert_called_once_with(directory)

    def test_is_directory_successfully_indexed_false(self):
        """Test checking if directory is indexed - returns False."""
        directory = "/test/books1"

        self.mock_job_repo.is_directory_successfully_indexed.return_value = False

        result = self.mock_job_repo.is_directory_successfully_indexed(directory)

        assert result is False
        self.mock_job_repo.is_directory_successfully_indexed.assert_called_once_with(directory)

    @patch('ebook_indexer.core.indexer.Path')
    def test_run_indexing_with_force_reindex_false(self, mock_path):
        """Test run_indexing with force_reindex=False skips indexed directories."""
        # Mock Path.exists to return True for all directories
        mock_path.return_value.exists.return_value = True

        directories = ["/test/books1", "/test/books2"]

        # Mock that books1 is already indexed, books2 is not
        self.mock_job_repo.get_directories_needing_indexing.return_value = ["/test/books2"]

        # Mock job creation and other dependencies
        mock_job = ProcessingJob(
            id="test_job_id",
            root_directories=["/test/books2"],
            status=JobStatus.PENDING
        )

        with patch.object(self.indexer, '_create_or_resume_job', return_value=mock_job), \
             patch.object(self.indexer, '_count_files', return_value=10), \
             patch.object(self.indexer, 'scanner') as mock_scanner, \
             patch.object(self.indexer, 'progress_tracker'), \
             patch.object(self.indexer, '_process_file'), \
             patch.object(self.indexer, '_complete_job'):

            mock_scanner.scan_multiple_directories.return_value = []

            result = self.indexer.run_indexing(
                root_directories=directories,
                force_reindex=False
            )

            # Should only process books2, not books1
            self.mock_job_repo.get_directories_needing_indexing.assert_called_once_with(directories)
            assert result == "test_job_id"

    @patch('ebook_indexer.core.indexer.Path')
    def test_run_indexing_with_force_reindex_true(self, mock_path):
        """Test run_indexing with force_reindex=True processes all directories."""
        # Mock Path.exists to return True for all directories
        mock_path.return_value.exists.return_value = True

        directories = ["/test/books1", "/test/books2"]

        # Mock job creation and other dependencies
        mock_job = ProcessingJob(
            id="test_job_id",
            root_directories=directories,
            status=JobStatus.PENDING
        )

        with patch.object(self.indexer, '_create_or_resume_job', return_value=mock_job), \
             patch.object(self.indexer, '_count_files', return_value=20), \
             patch.object(self.indexer, 'scanner') as mock_scanner, \
             patch.object(self.indexer, 'progress_tracker'), \
             patch.object(self.indexer, '_process_file'), \
             patch.object(self.indexer, '_complete_job'):

            mock_scanner.scan_multiple_directories.return_value = []

            result = self.indexer.run_indexing(
                root_directories=directories,
                force_reindex=True
            )

            # Should not call get_directories_needing_indexing when force=True
            self.mock_job_repo.get_directories_needing_indexing.assert_not_called()
            assert result == "test_job_id"

    @patch('ebook_indexer.core.indexer.Path')
    def test_run_indexing_all_directories_already_indexed(self, mock_path):
        """Test run_indexing when all directories are already indexed."""
        # Mock Path.exists to return True for all directories
        mock_path.return_value.exists.return_value = True

        directories = ["/test/books1", "/test/books2"]

        # Mock that all directories are already indexed
        self.mock_job_repo.get_directories_needing_indexing.return_value = []

        # Mock recent completed job
        mock_recent_job = ProcessingJob(
            id="recent_job_id",
            root_directories=directories,
            status=JobStatus.COMPLETED
        )
        self.mock_job_repo.get_completed_jobs_by_root_directories.return_value = [mock_recent_job]

        result = self.indexer.run_indexing(
            root_directories=directories,
            force_reindex=False
        )

        # Should return the recent job ID
        assert result == "recent_job_id"
        self.mock_job_repo.get_directories_needing_indexing.assert_called_once_with(directories)
        self.mock_job_repo.get_completed_jobs_by_root_directories.assert_called_once_with(directories)

    @patch('ebook_indexer.core.indexer.Path')
    def test_run_indexing_all_directories_indexed_no_previous_jobs(self, mock_path):
        """Test run_indexing when all directories are indexed but no previous jobs found."""
        # Mock Path.exists to return True for all directories
        mock_path.return_value.exists.return_value = True

        directories = ["/test/books1", "/test/books2"]

        # Mock that all directories are already indexed
        self.mock_job_repo.get_directories_needing_indexing.return_value = []

        # Mock no recent completed jobs
        self.mock_job_repo.get_completed_jobs_by_root_directories.return_value = []

        with pytest.raises(IndexingError, match="No directories to index and no previous jobs found"):
            self.indexer.run_indexing(
                root_directories=directories,
                force_reindex=False
            )

    def test_run_indexing_with_resume_skips_optimization(self):
        """Test that resume=True skips the optimization logic."""
        directories = ["/test/books1", "/test/books2"]

        # Mock job creation and other dependencies
        mock_job = ProcessingJob(
            id="test_job_id",
            root_directories=directories,
            status=JobStatus.PENDING
        )

        with patch('ebook_indexer.core.indexer.Path') as mock_path, \
             patch.object(self.indexer, '_create_or_resume_job', return_value=mock_job), \
             patch.object(self.indexer, '_count_files', return_value=20), \
             patch.object(self.indexer, 'scanner') as mock_scanner, \
             patch.object(self.indexer, 'progress_tracker'), \
             patch.object(self.indexer, '_process_file'), \
             patch.object(self.indexer, '_complete_job'):

            mock_path.return_value.exists.return_value = True
            mock_scanner.scan_multiple_directories.return_value = []

            result = self.indexer.run_indexing(
                root_directories=directories,
                resume=True
            )

            # Should not call optimization methods when resume=True
            self.mock_job_repo.get_directories_needing_indexing.assert_not_called()
            assert result == "test_job_id"
